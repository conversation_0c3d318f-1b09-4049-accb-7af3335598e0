/**
 ******************************************************************************
 * @file    motor_driver.h
 * @brief   DRV8871 电机驱动库头文件
 * <AUTHOR>
 * @date    2025-06-20
 ******************************************************************************
 * @attention
 *
 * 本库专为DRV8871电机驱动芯片设计
 * 支持双电机独立控制，实现DRV8871的多种控制模式
 * 支持浮点精度控制，1000级PWM精度
 *
 * === DRV8871快衰减模式技术说明 ===
 *
 * DRV8871支持两种衰减模式，其PWM控制原理不同：
 *
 * 1. 慢衰减模式（MOTOR_DECAY_SLOW）：
 *    - 正转：AIN1=PWM, AIN2=0，电机电压 = VCC * PWM
 *    - 反转：AIN1=0, AIN2=PWM，电机电压 = VCC * PWM
 *    - 特点：PWM占空比与转速成正比关系
 *
 * 2. 快衰减模式（MOTOR_DECAY_FAST）：
 *    - 正转：AIN1=PWM, AIN2=1，电机电压 = VCC * (PWM - 1)
 *    - 反转：AIN1=1, AIN2=PWM，电机电压 = VCC * (1 - PWM)
 *    - 特点：PWM占空比与转速成反比关系
 *
 * 快衰减模式PWM反转修复：
 * 为解决快衰减模式下占空比与转速反比的问题，本库在Speed1000_To_PWM
 * 函数中实现了PWM反转计算：
 * - 快衰减模式：实际PWM = MOTOR_PWM_PERIOD - 设置PWM
 * - 确保用户设置的占空比与实际转速一致
 *
 * 最小PWM阈值机制：
 * 为确保电机能够可靠启动，不同衰减模式设置了不同的最小PWM阈值：
 * - 快衰减模式：最小5% (50/999)，适合快速响应和低速启动
 * - 慢衰减模式：最小60% (599/999)，克服静摩擦确保可靠启动
 * - 低于阈值的设置值会自动提升到最小阈值
 *
 * 使用示例：
 * Motor_SetDecayMode(&motor, MOTOR_DECAY_FAST);
 * Motor_SetSpeed(&motor, 10.0f);  // 设置10%速度，实际获得10%转速
 *
 ******************************************************************************
 */

#ifndef __MOTOR_DRIVER_H__
#define __MOTOR_DRIVER_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"

/* Exported types ------------------------------------------------------------*/

/**
 * @brief 电机状态枚举
 */
typedef enum {
    MOTOR_STATE_STOP = 0,     // 停止/制动
    MOTOR_STATE_FORWARD,      // 正转
    MOTOR_STATE_BACKWARD,     // 反转
    MOTOR_STATE_ERROR         // 错误
} MotorState_t;

/**
 * @brief DRV8871衰减模式枚举
 */
typedef enum {
    MOTOR_DECAY_SLOW = 0,     // 慢衰减模式
    MOTOR_DECAY_FAST          // 快衰减模式
} MotorDecayMode_t;

/**
 * @brief 引脚模式枚举
 */
typedef enum {
    PIN_MODE_PWM = 0,         // PWM复用模式
    PIN_MODE_GPIO_LOW,        // GPIO输出低电平
    PIN_MODE_GPIO_HIGH        // GPIO输出高电平
} PinMode_t;

/**
 * @brief 电机硬件配置结构体 (DRV8871双PWM通道)
 */
typedef struct {
    TIM_HandleTypeDef* htim;        // PWM定时器句柄
    uint32_t ain1_channel;          // AIN1 PWM通道
    uint32_t ain2_channel;          // AIN2 PWM通道
    GPIO_TypeDef* ain1_port;        // AIN1 GPIO端口
    uint16_t ain1_pin;              // AIN1 GPIO引脚
    GPIO_TypeDef* ain2_port;        // AIN2 GPIO端口
    uint16_t ain2_pin;              // AIN2 GPIO引脚
    uint32_t ain1_af;               // AIN1 复用功能配置 (如GPIO_AF1_TIM1)
    uint32_t ain2_af;               // AIN2 复用功能配置 (如GPIO_AF1_TIM1)
    PinMode_t ain1_mode;            // AIN1 当前引脚模式
    PinMode_t ain2_mode;            // AIN2 当前引脚模式
} MotorHW_t;

/**
 * @brief 电机驱动实体结构体
 */
typedef struct {
    MotorHW_t hw;                   // 硬件配置
    float speed;                    // 当前速度 (-100.0 到 +100.0)
    MotorState_t state;             // 当前状态
    uint8_t enable;                 // 使能标志
    uint8_t reverse;                // 电机安装方向 (0-正装, 1-反装)
    MotorDecayMode_t decay_mode;    // 衰减模式 (慢衰减/快衰减)
} Motor_t;

/* Exported constants --------------------------------------------------------*/

/* === 硬件配置宏定义 === */
#define MOTOR_PWM_PERIOD        999      // PWM周期 (ARR值，与TIM1->ARR一致)

/* === 分衰减模式最小PWM阈值 === */
#define MOTOR_MIN_PWM_FAST_DECAY  50   // 快衰减最小PWM阈值 (5%: 50/999≈5%)
#define MOTOR_MIN_PWM_SLOW_DECAY  599  // 慢衰减最小PWM阈值 (60%: 599/999≈60%)

/* === 浮点速度范围宏定义 === */
#define MOTOR_SPEED_MAX_FLOAT   100.0f  // 浮点最大速度
#define MOTOR_SPEED_MIN_FLOAT   -100.0f // 浮点最小速度

/* === 精度控制宏定义 === */
#define MOTOR_MAX_PRECISION     1000    // 最大精度值 (1000级)
#define MOTOR_PRECISION_SCALE   10      // 精度倍数 (浮点转1000级)
/* Exported macros -----------------------------------------------------------*/

/* Exported functions prototypes ---------------------------------------------*/

/**
 * === DRV8871电机驱动库使用指南 ===
 *
 * 1. 基本使用流程：
 *    Motor_t motor;
 *    Motor_Create(&motor, &htim1, ...);     // 创建电机实体
 *    Motor_SetDecayMode(&motor, mode);      // 设置衰减模式
 *    Motor_SetSpeed(&motor, speed);         // 设置速度
 *
 * 2. 衰减模式选择：
 *    - MOTOR_DECAY_SLOW: 慢衰减，适合低速精确控制
 *    - MOTOR_DECAY_FAST: 快衰减，适合高速运行和快速响应
 *
 * 3. 快衰减模式注意事项：
 *    - 本库已自动处理PWM反转，用户无需关心底层实现
 *    - 设置的速度值与实际转速一致，无需额外计算
 *    - 支持动态切换衰减模式，实时生效
 *
 * 4. 最佳实践：
 *    - 低速精确定位：使用慢衰减模式
 *    - 高速运行：使用快衰减模式
 *    - 频繁启停：使用快衰减模式获得更好的响应
 */

/**
 * @brief 创建电机实体 (DRV8871双PWM通道)
 * @param motor: 电机实体指针
 * @param htim: PWM定时器句柄
 * @param ain1_channel: AIN1 PWM通道 (TIM_CHANNEL_1~4)
 * @param ain2_channel: AIN2 PWM通道 (TIM_CHANNEL_1~4)
 * @param ain1_port: AIN1 GPIO端口
 * @param ain1_pin: AIN1 GPIO引脚
 * @param ain1_af: AIN1 复用功能配置 (如GPIO_AF1_TIM1)
 * @param ain2_port: AIN2 GPIO端口
 * @param ain2_pin: AIN2 GPIO引脚
 * @param ain2_af: AIN2 复用功能配置 (如GPIO_AF1_TIM1)
 * @param reverse: 电机安装方向 (0-正装, 1-反装)
 * @retval 0: 成功, -1: 参数错误
 */
int8_t Motor_Create(Motor_t* motor,
                    TIM_HandleTypeDef* htim,
                    uint32_t ain1_channel,
                    uint32_t ain2_channel,
                    GPIO_TypeDef* ain1_port,
                    uint16_t ain1_pin,
                    uint32_t ain1_af,
                    GPIO_TypeDef* ain2_port,
                    uint16_t ain2_pin,
                    uint32_t ain2_af,
                    uint8_t reverse);

/**
 * @brief 设置电机速度
 * @param motor: 电机实体指针
 * @param speed: 速度值 (-100.0 到 +100.0)
 *               正数为正转，负数为反转，0.0为停止
 *               支持一位小数精度，如50.5
 * @retval 0: 成功, -1: 参数错误
 */
int8_t Motor_SetSpeed(Motor_t* motor, float speed);

/**
 * @brief 停止电机
 * @param motor: 电机实体指针
 * @retval 0: 成功, -1: 参数错误
 */
int8_t Motor_Stop(Motor_t* motor);


/**
 * @brief 获取电机状态
 * @param motor: 电机实体指针
 * @retval 电机状态
 */
MotorState_t Motor_GetState(Motor_t* motor);

/**
 * @brief 使能/失能电机
 * @param motor: 电机实体指针
 * @param enable: 1-使能, 0-失能
 * @retval 0: 成功, -1: 参数错误
 */
int8_t Motor_Enable(Motor_t* motor, uint8_t enable);

/**
 * @brief 设置电机衰减模式
 * @param motor: 电机实体指针
 * @param mode: 衰减模式 (MOTOR_DECAY_SLOW/MOTOR_DECAY_FAST)
 * @retval 0: 成功, -1: 参数错误
 */
int8_t Motor_SetDecayMode(Motor_t* motor, MotorDecayMode_t mode);

#ifdef __cplusplus
}
#endif

#endif /* __MOTOR_DRIVER_H__ */

