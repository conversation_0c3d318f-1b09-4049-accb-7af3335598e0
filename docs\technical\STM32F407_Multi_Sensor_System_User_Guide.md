# STM32F407多传感器系统使用指南

## 文档信息
- **版本**: v1.0
- **创建日期**: 2025-07-28
- **适用项目**: STM32F407多传感器集成系统
- **作者**: 米醋电子工作室技术团队

---

## 目录
1. [系统概述](#1-系统概述)
2. [JY901S姿态传感器](#2-jy901s姿态传感器)
3. [灰度传感器](#3-灰度传感器)
4. [DRV8871电机驱动](#4-drv8871电机驱动)
5. [EMMV5步进电机](#5-emmv5步进电机)
6. [OLED显示屏](#6-oled显示屏)
7. [完整初始化流程](#7-完整初始化流程)
8. [主循环调用示例](#8-主循环调用示例)

---

## 1. 系统概述

### 1.1 硬件配置
| 模块 | 通信接口 | 功能描述 |
|------|----------|----------|
| **JY901S姿态传感器** | UART5 (9600) | 三轴角度、加速度、角速度检测 |
| **灰度传感器** | I2C (软件模拟) | 8路数字量+8路模拟量检测 |
| **DRV8871电机驱动** | PWM (TIM1) | 双路直流电机控制 |
| **EMMV5步进电机** | USART2/UART4 (115200) | X/Y轴步进电机精确控制 |
| **OLED显示屏** | I2C1/I2C2 | 实时数据显示 |

### 1.2 系统特性
- **实时性**: 10Hz主循环刷新率
- **模块化**: 独立的驱动模块设计
- **可扩展**: 支持多传感器集成
- **稳定性**: 完善的错误处理机制

---

## 2. JY901S姿态传感器

### 2.1 基础准备

#### 2.1.1 硬件连接
```
JY901S    ->  STM32F407
VCC       ->  3.3V/5V
GND       ->  GND
TX        ->  PC12 (UART5_RX)
RX        ->  PD2  (UART5_TX)
```

#### 2.1.2 必要的头文件包含
```c
#include "jy901s.h"
```

#### 2.1.3 全局变量声明
```c
extern UART_HandleTypeDef huart5;
JY901S_Handle jy901s_handle;  // JY901S传感器句柄
JY901S_Data sensor_data;      // 传感器数据缓存
uint8_t uart_rx_buffer[1];    // UART接收缓冲区
```

### 2.2 初始化流程

#### 2.2.1 UART5初始化 (CubeMX自动生成)
```c
// 在main()函数中调用
MX_UART5_Init();  // 9600波特率，8N1
```

#### 2.2.2 JY901S传感器初始化
```c
// 初始化JY901S传感器
JY901S_Init(&jy901s_handle, &huart5, NULL);

// 启动UART中断接收
HAL_UART_Receive_IT(&huart5, uart_rx_buffer, 1);
```

#### 2.2.3 中断回调函数配置
```c
// 在main.c中实现UART接收完成回调
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
  if (huart->Instance == UART5)
  {
    // 处理接收到的字节
    JY901S_ProcessByte(&jy901s_handle, uart_rx_buffer[0]);
    
    // 重新启动接收
    HAL_UART_Receive_IT(&huart5, uart_rx_buffer, 1);
  }
}
```

### 2.3 可用功能函数

#### 2.3.1 数据获取函数
```c
// 获取传感器数据
JY901S_GetData(&jy901s_handle, &sensor_data);

// 访问具体数据
float roll = sensor_data.angle[0];   // 横滚角 (-180° ~ +180°)
float pitch = sensor_data.angle[1];  // 俯仰角 (-90° ~ +90°)
float yaw = sensor_data.angle[2];    // 偏航角 (-180° ~ +180°)

float acc_x = sensor_data.acc[0];    // X轴加速度 (g)
float acc_y = sensor_data.acc[1];    // Y轴加速度 (g)
float acc_z = sensor_data.acc[2];    // Z轴加速度 (g)

float gyro_x = sensor_data.gyro[0];  // X轴角速度 (°/s)
float gyro_y = sensor_data.gyro[1];  // Y轴角速度 (°/s)
float gyro_z = sensor_data.gyro[2];  // Z轴角速度 (°/s)
```

#### 2.3.2 状态检查函数
```c
// 检查数据是否有效
if (jy901s_handle.data_ready) {
    // 数据已更新，可以读取
    JY901S_GetData(&jy901s_handle, &sensor_data);
}
```

#### 2.3.3 调试函数
```c
// 打印传感器数据 (需要配置printf重定向)
JY901S_PrintData(&jy901s_handle);

// 重置解析器
JY901S_ResetParser(&jy901s_handle);
```

---

## 3. 灰度传感器

### 3.1 基础准备

#### 3.1.1 硬件连接
```
灰度传感器  ->  STM32F407
VCC        ->  3.3V/5V
GND        ->  GND
SCL        ->  PC0 (软件I2C)
SDA        ->  PC1 (软件I2C)
```

#### 3.1.2 必要的头文件包含
```c
#include "software_iic.h"
```

#### 3.1.3 全局变量声明
```c
uint8_t gray_digital_data = 0;      // 8位数字量数据
uint8_t gray_analog_data[8] = {0};  // 8路模拟量数据
uint8_t gray_sensor_connected = 0;  // 传感器连接状态
```

### 3.2 初始化流程

#### 3.2.1 GPIO初始化 (CubeMX自动生成)
```c
// PC0, PC1配置为开漏输出，上拉
MX_GPIO_Init();
```

#### 3.2.2 灰度传感器初始化
```c
// 灰度传感器初始化
void GrayScale_Init(void)
{
  // 延时等待传感器稳定
  HAL_Delay(100);
  
  // I2C地址扫描
  uint8_t addr_found = 0;
  for (uint8_t addr = 0x10; addr <= 0x7F; addr++) {
    if (I2C_ScanAddress(addr) == 0) {
      if (addr == 0x4C) {
        addr_found = 1;
        break;
      }
    }
    HAL_Delay(1);
  }
  
  // 检测传感器连接状态
  if (addr_found && Ping() == 0) {
    gray_sensor_connected = 1;
  } else {
    gray_sensor_connected = 0;
  }
}
```

### 3.3 可用功能函数

#### 3.3.1 数据读取函数
```c
// 读取灰度传感器数据
void GrayScale_ReadData(void)
{
  if (gray_sensor_connected) {
    // 读取数字量数据 (8位)
    gray_digital_data = IIC_Get_Digtal();
    
    // 读取模拟量数据 (8路)
    IIC_Get_Anolog(gray_analog_data, 8);
  }
}
```

#### 3.3.2 数据访问宏
```c
// 获取指定位的数字量数据
#define GET_NTH_BIT(data, n) (((data) >> ((n) - 1)) & 0x01)

// 使用示例
uint8_t bit1 = GET_NTH_BIT(gray_digital_data, 1);  // 获取第1位
uint8_t bit8 = GET_NTH_BIT(gray_digital_data, 8);  // 获取第8位
```

#### 3.3.3 底层I2C函数
```c
// I2C地址扫描
uint8_t I2C_ScanAddress(uint8_t addr);

// 传感器连通性检测
uint8_t Ping(void);

// 软件I2C基础函数
void IIC_Start(void);
void IIC_Stop(void);
uint8_t IIC_SendByte(uint8_t data);
uint8_t IIC_ReadByte(void);
```

---

## 4. DRV8871电机驱动

### 4.1 基础准备

#### 4.1.1 硬件连接
```
DRV8871_1   ->  STM32F407     DRV8871_2   ->  STM32F407
VCC         ->  12V           VCC         ->  12V
GND         ->  GND           GND         ->  GND
IN1         ->  PE9 (TIM1_CH1) IN1        ->  PE11 (TIM1_CH2)
IN2         ->  PE8 (GPIO)     IN2        ->  PE10 (GPIO)
OUT1/OUT2   ->  电机1          OUT1/OUT2   ->  电机2
```

#### 4.1.2 必要的头文件包含
```c
#include "motor_driver.h"
```

#### 4.1.3 全局变量声明
```c
extern TIM_HandleTypeDef htim1;
Motor_Handle motor_left;   // 左电机句柄
Motor_Handle motor_right;  // 右电机句柄
```

### 4.2 初始化流程

#### 4.2.1 TIM1和GPIO初始化 (CubeMX自动生成)
```c
MX_TIM1_Init();  // PWM配置
MX_GPIO_Init();  // 方向控制引脚
```

#### 4.2.2 电机驱动初始化
```c
void Motor_Init(void)
{
  // 初始化左电机 (TIM1_CH1, PE8)
  Motor_InitHandle(&motor_left, &htim1, TIM_CHANNEL_1, 
                   GPIOE, GPIO_PIN_8);
  
  // 初始化右电机 (TIM1_CH2, PE10)  
  Motor_InitHandle(&motor_right, &htim1, TIM_CHANNEL_2, 
                   GPIOE, GPIO_PIN_10);
  
  // 启动PWM输出
  HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_1);
  HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_2);
}
```

### 4.3 可用功能函数

#### 4.3.1 速度控制函数
```c
// 设置电机速度 (-100.0 ~ +100.0)
Motor_SetSpeed(&motor_left, 50.0f);   // 左电机正转50%
Motor_SetSpeed(&motor_right, -30.0f); // 右电机反转30%
Motor_SetSpeed(&motor_left, 0.0f);    // 左电机停止
```

#### 4.3.2 方向控制函数
```c
// 设置电机方向
Motor_SetDirection(&motor_left, MOTOR_FORWARD);   // 正转
Motor_SetDirection(&motor_right, MOTOR_BACKWARD); // 反转
Motor_SetDirection(&motor_left, MOTOR_BRAKE);     // 刹车
```

#### 4.3.3 PWM控制函数
```c
// 直接设置PWM占空比 (0-1000)
Motor_SetPWM(&motor_left, 500);  // 50%占空比
Motor_SetPWM(&motor_right, 800); // 80%占空比
```

#### 4.3.4 状态查询函数
```c
// 获取当前速度
float current_speed = Motor_GetSpeed(&motor_left);

// 获取当前方向
Motor_Direction dir = Motor_GetDirection(&motor_right);
```

---

## 5. EMMV5步进电机

### 5.1 基础准备

#### 5.1.1 硬件连接
```
EMMV5_X轴   ->  STM32F407     EMMV5_Y轴   ->  STM32F407
VCC         ->  24V           VCC         ->  24V
GND         ->  GND           GND         ->  GND
TX          ->  PA2 (USART2_TX) TX        ->  PC10 (UART4_TX)
RX          ->  PA3 (USART2_RX) RX        ->  PC11 (UART4_RX)
```

#### 5.1.2 必要的头文件包含
```c
#include "Emm_V5.h"
```

#### 5.1.3 全局变量声明
```c
extern UART_HandleTypeDef huart2;  // X轴通信
extern UART_HandleTypeDef huart4;  // Y轴通信

#define EMMV5_X_MOTOR_ADDR  0x01   // X轴电机地址
#define EMMV5_Y_MOTOR_ADDR  0x01   // Y轴电机地址
```

### 5.2 初始化流程

#### 5.2.1 UART初始化 (CubeMX自动生成)
```c
MX_USART2_UART_Init();  // 115200波特率
MX_UART4_Init();        // 115200波特率
```

#### 5.2.2 EMMV5电机初始化
```c
void EMMV5_Init(void)
{
  // 延时等待电机稳定
  HAL_Delay(100);
  
  // 初始化X轴电机 (USART2)
  Emm_V5_Modify_Ctrl_Mode(&huart2, EMMV5_X_MOTOR_ADDR, false, 2);
  HAL_Delay(10);
  Emm_V5_En_Control(&huart2, EMMV5_X_MOTOR_ADDR, true, false);
  HAL_Delay(10);
  
  // 初始化Y轴电机 (UART4)
  Emm_V5_Modify_Ctrl_Mode(&huart4, EMMV5_Y_MOTOR_ADDR, false, 2);
  HAL_Delay(10);
  Emm_V5_En_Control(&huart4, EMMV5_Y_MOTOR_ADDR, true, false);
  HAL_Delay(10);
}
```

### 5.3 可用功能函数

#### 5.3.1 电机使能控制
```c
// 使能电机
Emm_V5_En_Control(&huart2, EMMV5_X_MOTOR_ADDR, true, false);

// 禁用电机
Emm_V5_En_Control(&huart2, EMMV5_X_MOTOR_ADDR, false, false);
```

#### 5.3.2 控制模式设置
```c
// 设置控制模式
// ctrl_mode: 0=关闭脉冲, 1=开环, 2=闭环, 3=限位模式
Emm_V5_Modify_Ctrl_Mode(&huart2, EMMV5_X_MOTOR_ADDR, false, 2);
```

#### 5.3.3 速度模式控制
```c
// 速度模式控制
// dir: 0=CW正转, 1=CCW反转
// vel: 速度 0-5000 RPM
// acc: 加速度 0-255 (0为直接启动)
Emm_V5_Vel_Control(&huart2, EMMV5_X_MOTOR_ADDR, 0, 100, 10, false);
```

#### 5.3.4 位置模式控制
```c
// 位置模式控制
// clk: 脉冲数 0-(2^32-1)
// raF: false=相对运动, true=绝对运动
Emm_V5_Pos_Control(&huart2, EMMV5_X_MOTOR_ADDR, 0, 100, 10, 
                   1600, false, false);  // 相对运动1600脉冲
```

#### 5.3.5 停止控制
```c
// 立即停止电机
Emm_V5_Stop_Now(&huart2, EMMV5_X_MOTOR_ADDR, false);
```

#### 5.3.6 位置清零
```c
// 将当前位置设为零点
Emm_V5_Reset_CurPos_To_Zero(&huart2, EMMV5_X_MOTOR_ADDR);
```

#### 5.3.7 回零功能
```c
// 触发回零
// o_mode: 0=单圈就近, 1=单圈方向, 2=多圈碰撞, 3=多圈限位
Emm_V5_Origin_Trigger_Return(&huart2, EMMV5_X_MOTOR_ADDR, 0, false);
```

---

## 6. OLED显示屏

### 6.1 基础准备

#### 6.1.1 硬件连接
```
OLED       ->  STM32F407
VCC        ->  3.3V
GND        ->  GND
SCL        ->  PB6 (I2C1_SCL)
SDA        ->  PB7 (I2C1_SDA)
```

#### 6.1.2 必要的头文件包含
```c
#include "oled.h"
```

### 6.2 初始化流程

#### 6.2.1 I2C初始化 (CubeMX自动生成)
```c
MX_I2C1_Init();  // 或 MX_I2C2_Init()
```

#### 6.2.2 OLED初始化
```c
// OLED初始化
OLED_Init();
OLED_Clear();
```

### 6.3 可用功能函数

#### 6.3.1 基础显示函数
```c
// 显示字符串
OLED_ShowStr(0, 0, "Hello", 16);     // 16号字体
OLED_ShowStr(0, 2, "World", 8);      // 8号字体

// 显示数字
OLED_ShowNum(0, 4, 12345, 5, 16);    // 显示5位数字

// 清屏
OLED_Clear();
```

#### 6.3.2 自定义显示函数
```c
// 显示带符号角度 (项目自定义)
void OLED_ShowSignedAngle(uint8_t x, uint8_t y, int16_t angle);

// 显示灰度传感器数据 (项目自定义)
void OLED_ShowGrayData(void);

// 显示电机状态 (项目自定义)
void OLED_ShowMotorStatus(void);
```

---

## 7. 完整初始化流程

### 7.1 main()函数初始化顺序

```c
int main(void)
{
  /* MCU Configuration */
  HAL_Init();
  SystemClock_Config();

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_I2C2_Init();
  MX_I2C1_Init();
  MX_UART5_Init();        // JY901S通信
  MX_TIM1_Init();         // 电机PWM
  MX_USART2_UART_Init();  // EMMV5 X轴通信
  MX_UART4_Init();        // EMMV5 Y轴通信

  /* USER CODE BEGIN 2 */

  // 1. OLED初始化 (最先初始化，用于显示状态)
  OLED_Init();
  OLED_Clear();

  // 2. JY901S姿态传感器初始化
  JY901S_Init(&jy901s_handle, &huart5, NULL);
  HAL_UART_Receive_IT(&huart5, uart_rx_buffer, 1);

  // 3. 灰度传感器初始化
  GrayScale_Init();

  // 4. DRV8871电机驱动初始化
  Motor_Init();

  // 5. EMMV5步进电机初始化 (X轴和Y轴)
  EMMV5_Init();

  // 6. 显示初始化完成信息
  OLED_ShowStr(0, 0, "STM32F407", 16);
  OLED_ShowStr(0, 2, "Multi-Sensor+EMMV5", 8);
  HAL_Delay(2000);  // 显示2秒
  OLED_Clear();

  /* USER CODE END 2 */

  /* Infinite loop */
  while (1)
  {
    // 主循环代码 (见第8章)
  }
}
```

### 7.2 中断回调函数配置

```c
// UART接收完成回调函数
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
  if (huart->Instance == UART5)
  {
    // 处理JY901S数据
    JY901S_ProcessByte(&jy901s_handle, uart_rx_buffer[0]);

    // 重新启动接收
    HAL_UART_Receive_IT(&huart5, uart_rx_buffer, 1);
  }
}
```

### 7.3 必要的全局变量声明

```c
/* 外部句柄声明 */
extern I2C_HandleTypeDef hi2c1;
extern I2C_HandleTypeDef hi2c2;
extern TIM_HandleTypeDef htim1;
extern UART_HandleTypeDef huart4;
extern UART_HandleTypeDef huart5;
extern UART_HandleTypeDef huart2;

/* JY901S相关变量 */
JY901S_Handle jy901s_handle;
JY901S_Data sensor_data;
uint8_t uart_rx_buffer[1];

/* 灰度传感器相关变量 */
uint8_t gray_digital_data = 0;
uint8_t gray_analog_data[8] = {0};
uint8_t gray_sensor_connected = 0;

/* 电机相关变量 */
Motor_Handle motor_left;
Motor_Handle motor_right;

/* EMMV5电机相关变量 */
#define EMMV5_X_MOTOR_ADDR  0x01
#define EMMV5_Y_MOTOR_ADDR  0x01
uint32_t emmv5_test_counter = 0;
```

---

## 8. 主循环调用示例

### 8.1 标准主循环结构

```c
while (1)
{
  /* USER CODE BEGIN 3 */

  // 1. 获取JY901S传感器数据
  JY901S_GetData(&jy901s_handle, &sensor_data);

  // 2. 读取灰度传感器数据
  GrayScale_ReadData();

  // 3. 电机控制 (根据需要选择)
  // Motor_Test();           // DRV8871电机测试 (已暂停)
  EMMV5_Test();             // EMMV5步进电机测试

  // 4. 清屏并显示传感器数据
  OLED_Clear();

  // 5. 显示JY901S数据
  OLED_ShowStr(0, 0, "R:", 8);
  OLED_ShowSignedAngle(16, 0, (int16_t)sensor_data.angle[0]);

  OLED_ShowStr(0, 1, "P:", 8);
  OLED_ShowSignedAngle(16, 1, (int16_t)sensor_data.angle[1]);

  OLED_ShowStr(0, 2, "Y:", 8);
  OLED_ShowSignedAngle(16, 2, (int16_t)sensor_data.angle[2]);

  // 6. 显示灰度传感器和电机状态
  OLED_ShowGrayData();      // 第4行左侧
  OLED_ShowMotorStatus();   // 第4行右侧

  // 7. 延时100ms，实现约10Hz刷新率
  HAL_Delay(100);

  /* USER CODE END 3 */
}
```

### 8.2 自定义控制示例

#### 8.2.1 基于姿态的电机控制
```c
// 根据俯仰角控制EMMV5 X轴
if (sensor_data.angle[1] > 10.0f) {
  // 前倾，X轴正转
  Emm_V5_Vel_Control(&huart2, EMMV5_X_MOTOR_ADDR, 0, 50, 5, false);
} else if (sensor_data.angle[1] < -10.0f) {
  // 后倾，X轴反转
  Emm_V5_Vel_Control(&huart2, EMMV5_X_MOTOR_ADDR, 1, 50, 5, false);
} else {
  // 平衡，停止
  Emm_V5_Stop_Now(&huart2, EMMV5_X_MOTOR_ADDR, false);
}
```

#### 8.2.2 基于灰度传感器的路径跟踪
```c
if (gray_sensor_connected) {
  // 计算左右传感器差值
  uint8_t left_sensors = gray_analog_data[0] + gray_analog_data[1];
  uint8_t right_sensors = gray_analog_data[6] + gray_analog_data[7];

  if (left_sensors > right_sensors + 20) {
    // 左侧检测到线，右转
    Motor_SetSpeed(&motor_left, 30.0f);
    Motor_SetSpeed(&motor_right, 10.0f);
  } else if (right_sensors > left_sensors + 20) {
    // 右侧检测到线，左转
    Motor_SetSpeed(&motor_left, 10.0f);
    Motor_SetSpeed(&motor_right, 30.0f);
  } else {
    // 直行
    Motor_SetSpeed(&motor_left, 25.0f);
    Motor_SetSpeed(&motor_right, 25.0f);
  }
}
```

#### 8.2.3 EMMV5精确定位控制
```c
// X轴移动到指定位置 (相对运动)
void EMMV5_MoveX(int32_t steps, uint16_t speed)
{
  uint8_t direction = (steps >= 0) ? 0 : 1;  // 0=正转, 1=反转
  uint32_t abs_steps = (steps >= 0) ? steps : -steps;

  Emm_V5_Pos_Control(&huart2, EMMV5_X_MOTOR_ADDR, direction,
                     speed, 10, abs_steps, false, false);
}

// Y轴移动到指定位置 (相对运动)
void EMMV5_MoveY(int32_t steps, uint16_t speed)
{
  uint8_t direction = (steps >= 0) ? 0 : 1;
  uint32_t abs_steps = (steps >= 0) ? steps : -steps;

  Emm_V5_Pos_Control(&huart4, EMMV5_Y_MOTOR_ADDR, direction,
                     speed, 10, abs_steps, false, false);
}

// 使用示例
EMMV5_MoveX(1600, 100);   // X轴正向移动1600步，速度100RPM
EMMV5_MoveY(-800, 50);    // Y轴负向移动800步，速度50RPM
```

---

## 9. 常见问题与解决方案

### 9.1 JY901S传感器问题

**问题**: 数据不更新
**解决方案**:
1. 检查UART5连接和波特率设置
2. 确认中断回调函数正确实现
3. 检查`jy901s_handle.data_ready`标志

**问题**: 角度数据跳变
**解决方案**:
1. 增加数据滤波算法
2. 检查传感器安装是否牢固
3. 避免强磁场干扰

### 9.2 灰度传感器问题

**问题**: 传感器检测不到
**解决方案**:
1. 检查I2C连接和上拉电阻
2. 确认传感器地址为0x4C
3. 增加初始化延时时间

### 9.3 EMMV5电机问题

**问题**: 电机不转动
**解决方案**:
1. 检查UART通信连接
2. 确认电机电源供应充足
3. 检查电机地址设置
4. 确认控制模式为闭环模式

**问题**: 电机运动不平稳
**解决方案**:
1. 调整加速度参数
2. 降低运行速度
3. 检查机械负载

---

## 10. 技术参数总结

### 10.1 通信参数
| 模块 | 接口 | 波特率 | 数据格式 |
|------|------|--------|----------|
| JY901S | UART5 | 9600 | 8N1 |
| 灰度传感器 | 软件I2C | 100kHz | 标准I2C |
| EMMV5 X轴 | USART2 | 115200 | 8N1 |
| EMMV5 Y轴 | UART4 | 115200 | 8N1 |
| OLED | I2C1/I2C2 | 400kHz | 标准I2C |

### 10.2 性能参数
- **主循环频率**: 10Hz
- **JY901S更新率**: 200Hz
- **灰度传感器精度**: 8位ADC
- **EMMV5速度范围**: 0-5000 RPM
- **DRV8871功率**: 最大45A

---

**文档状态**: ✅ 完成
**最后更新**: 2025-07-28
**版权所有**: 米醋电子工作室
