# 灰度传感器集成说明文档

## 1. 集成概述

### 1.1 传感器信息
- **型号**: 感为智能8路灰度传感器
- **通信方式**: I2C (软件模拟)
- **地址**: 0x4C (默认)
- **功能**: 8路灰度检测，支持数字量和模拟量输出

### 1.2 集成目标
- ✅ **驱动集成**: 将灰度传感器驱动添加到STM32F407项目
- ✅ **GPIO配置**: 配置PC4/PC5作为软件I2C引脚
- ✅ **显示集成**: 在OLED上显示灰度传感器数据
- ✅ **实时监控**: 与JY901S传感器同时工作

## 2. 硬件连接

### 2.1 引脚配置
| 功能 | STM32引脚 | 传感器引脚 | 说明 |
|------|-----------|------------|------|
| SCL  | PC4       | SCL        | I2C时钟线 |
| SDA  | PC5       | SDA        | I2C数据线 |
| VCC  | 3.3V      | VCC        | 电源正极 |
| GND  | GND       | GND        | 电源负极 |

### 2.2 GPIO配置代码
```c
// GPIO初始化配置 (已在MX_GPIO_Init中配置)
GPIO_InitStruct.Pin = GRAY_SOFT_SCL_Pin|GRAY_SOFT_SDA_Pin;
GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
GPIO_InitStruct.Pull = GPIO_PULLUP;
GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
```

## 3. 软件集成

### 3.1 驱动文件集成
**添加的文件**:
- `Moudle/grayscale/gw_grayscale_sensor.h` - 传感器定义和宏
- `Moudle/grayscale/software_iic.h` - 软件I2C接口声明
- `Moudle/grayscale/software_iic.c` - 软件I2C实现

**项目配置更新**:
- 包含路径: 添加`Moudle/grayscale`
- 源文件: 添加`software_iic.c`到编译列表

### 3.2 主程序集成

#### 头文件包含
```c
#include "software_iic.h"
```

#### 全局变量定义
```c
// 灰度传感器相关变量
uint8_t gray_digital_data = 0;      // 8位数字量数据
uint8_t gray_analog_data[8] = {0};  // 8路模拟量数据
uint8_t gray_sensor_connected = 0;  // 传感器连接状态
```

#### 函数声明
```c
void GrayScale_Init(void);
void GrayScale_ReadData(void);
void OLED_ShowGrayData(void);
```

### 3.3 初始化流程
```c
// 在main函数中的初始化顺序
OLED_Init();                    // OLED初始化
JY901S_Init(&jy901s_handle, &huart5, NULL);  // JY901S初始化
HAL_UART_Receive_IT(&huart5, uart_rx_buffer, 1);  // UART中断
GrayScale_Init();               // 灰度传感器初始化
```

## 4. 功能实现

### 4.1 传感器初始化函数
```c
void GrayScale_Init(void)
{
  // 检测传感器连接状态
  if (Ping() == 0) {
    gray_sensor_connected = 1;
  } else {
    gray_sensor_connected = 0;
  }
}
```

### 4.2 数据读取函数
```c
void GrayScale_ReadData(void)
{
  if (gray_sensor_connected) {
    // 读取数字量数据
    gray_digital_data = IIC_Get_Digtal();
    
    // 读取模拟量数据
    IIC_Get_Anolog(gray_analog_data, 8);
  }
}
```

### 4.3 显示函数
```c
void OLED_ShowGrayData(void)
{
  if (gray_sensor_connected) {
    // 显示数字量数据 (8位二进制)
    OLED_ShowStr(0, 3, "G:", 8);
    
    // 显示前4位数字量
    for (int i = 0; i < 4; i++) {
      uint8_t bit = GET_NTH_BIT(gray_digital_data, i + 1);
      OLED_ShowNum(16 + i * 8, 3, bit, 1, 8);
    }
    
    // 显示后4位数字量
    for (int i = 0; i < 4; i++) {
      uint8_t bit = GET_NTH_BIT(gray_digital_data, i + 5);
      OLED_ShowNum(56 + i * 8, 3, bit, 1, 8);
    }
    
    // 显示模拟量平均值
    uint16_t avg = 0;
    for (int i = 0; i < 8; i++) {
      avg += gray_analog_data[i];
    }
    avg /= 8;
    OLED_ShowStr(96, 3, "A:", 8);
    OLED_ShowNum(112, 3, avg, 3, 8);
  } else {
    OLED_ShowStr(0, 3, "Gray: Disconnected", 8);
  }
}
```

## 5. 显示布局

### 5.1 新的OLED布局 (128x32, 4行)
```
┌─────────────────────────────┐
│ JY+Gray    RX:123  OK       │  ← 标题+状态
│ R: -45  P:  30              │  ← Roll/Pitch角度
│ Y: 120                      │  ← Yaw角度
│ G:01010101 A:128            │  ← 灰度数据
└─────────────────────────────┘
```

### 5.2 显示内容说明
- **第1行**: 
  - `JY+Gray`: 系统标识
  - `RX:123`: UART接收计数
  - `OK/ER`: 灰度传感器连接状态
- **第2行**: Roll和Pitch角度 (-180°~+180°)
- **第3行**: Yaw角度 (-180°~+180°)
- **第4行**: 
  - `G:01010101`: 8位数字量数据
  - `A:128`: 8路模拟量平均值

## 6. 数据格式

### 6.1 数字量数据
- **格式**: 8位二进制数据
- **含义**: 每位代表一个探头的黑白检测结果
- **值**: 0=白色/浅色, 1=黑色/深色
- **显示**: 直接显示8个0/1数字

### 6.2 模拟量数据
- **格式**: 8个字节，每个字节0-255
- **含义**: 每个探头的灰度强度值
- **值**: 0=最白, 255=最黑
- **显示**: 显示8路平均值

### 6.3 连接状态
- **检测方式**: 发送Ping命令(0xAA)，期望返回0x66
- **状态显示**: OK=连接正常, ER=连接异常
- **影响**: 连接异常时不读取数据，显示错误信息

## 7. 软件I2C实现

### 7.1 时序参数
- **时钟频率**: 约50kHz (10μs延时)
- **引脚配置**: 开漏输出，上拉使能
- **延时精度**: 基于SysTick，适配168MHz系统时钟

### 7.2 关键函数
- `IIC_Start()`: I2C起始信号
- `IIC_Stop()`: I2C停止信号
- `IIC_SendByte()`: 发送字节数据
- `IIC_RecvByte()`: 接收字节数据
- `IIC_WaitAck()`: 等待应答信号

### 7.3 应用接口
- `Ping()`: 传感器连接检测
- `IIC_Get_Digtal()`: 获取数字量数据
- `IIC_Get_Anolog()`: 获取模拟量数据
- `IIC_Get_Single_Anolog()`: 获取单通道模拟量

## 8. 性能特点

### 8.1 实时性
- **刷新率**: 10Hz (与JY901S同步)
- **响应时间**: I2C通信约1ms
- **显示延迟**: 总延迟<10ms

### 8.2 可靠性
- **连接检测**: 每次启动时检测传感器连接
- **错误处理**: 连接失败时显示错误信息
- **数据校验**: I2C通信包含应答检测

### 8.3 兼容性
- **多传感器**: 与JY901S传感器同时工作
- **资源占用**: 软件I2C不占用硬件I2C资源
- **引脚复用**: 使用独立的GPIO引脚

## 9. 调试和测试

### 9.1 连接测试
```c
// 测试传感器连接
if (Ping() == 0) {
    printf("Grayscale sensor connected\n");
} else {
    printf("Grayscale sensor not found\n");
}
```

### 9.2 数据验证
```c
// 验证数据读取
uint8_t digital = IIC_Get_Digtal();
printf("Digital data: 0x%02X\n", digital);

uint8_t analog[8];
IIC_Get_Anolog(analog, 8);
for (int i = 0; i < 8; i++) {
    printf("Channel %d: %d\n", i+1, analog[i]);
}
```

### 9.3 常见问题
- **连接失败**: 检查引脚连接和电源
- **数据异常**: 检查I2C时序和地址设置
- **显示错误**: 检查OLED显示函数调用

## 10. 后续扩展

### 10.1 功能增强
- **阈值设置**: 支持动态调整黑白检测阈值
- **滤波算法**: 添加数据滤波减少噪声
- **标定功能**: 支持传感器标定和校准

### 10.2 显示优化
- **图形显示**: 添加灰度条形图显示
- **历史数据**: 显示灰度变化趋势
- **多页显示**: 支持详细数据页面切换

---

**文档版本**: v1.0  
**集成时间**: 2025-07-28  
**传感器型号**: 感为智能8路灰度传感器  
**负责人**: Alex (工程师)  
**测试状态**: 待验证
