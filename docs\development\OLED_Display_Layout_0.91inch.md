# 0.91寸OLED显示布局设计文档

## 1. 硬件规格
- **屏幕尺寸**: 0.91寸
- **分辨率**: 128×32像素
- **显示行数**: 4行 (每行8像素高度)
- **字符宽度**: 6像素 (8号字体)
- **每行字符数**: 约21个字符

## 2. 显示布局设计

### 2.1 整体布局
```
┌─────────────────────────────────────────────────────────────┐
│ 第1行 │ JY901S        RX:1234                                │
│ 第2行 │ R:123    P:456                                       │
│ 第3行 │ Y:789    AX:12                                       │
│ 第4行 │ AY:34    AZ:56                                       │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 详细布局说明

#### 第1行 (Y=0): 标题和状态信息
```
位置    内容        说明
0-40    "JY901S"    传感器名称标识
48-72   "RX:"       接收状态标签
72-120  数字        接收字节计数(后4位)
```

#### 第2行 (Y=1): 横滚角和俯仰角
```
位置    内容    说明
0-16    "R:"    Roll横滚角标签
16-48   数字    横滚角度值(-180~+180°)
64-80   "P:"    Pitch俯仰角标签
80-112  数字    俯仰角度值(-90~+90°)
```

#### 第3行 (Y=2): 偏航角和X轴加速度
```
位置    内容    说明
0-16    "Y:"    Yaw偏航角标签
16-48   数字    偏航角度值(0~360°)
64-88   "AX:"   X轴加速度标签
88-120  数字    X轴加速度值(×10显示)
```

#### 第4行 (Y=3): Y轴和Z轴加速度
```
位置    内容    说明
0-24    "AY:"   Y轴加速度标签
24-56   数字    Y轴加速度值(×10显示)
64-88   "AZ:"   Z轴加速度标签
88-120  数字    Z轴加速度值(×10显示)
```

## 3. 数据显示格式

### 3.1 角度数据
- **显示范围**: -180° ~ +180°
- **显示精度**: 整数度
- **显示格式**: 3位数字 (含负号)
- **示例**: "123", "-45", "  7"

### 3.2 加速度数据
- **显示范围**: -20g ~ +20g
- **显示精度**: 0.1g
- **显示格式**: 3位数字 (×10后的整数)
- **示例**: "123" 表示 12.3g

### 3.3 接收计数
- **显示范围**: 0 ~ 9999 (循环显示)
- **显示格式**: 4位数字
- **用途**: 调试UART接收状态

## 4. 显示优化策略

### 4.1 空间优化
- 使用缩写标签节省空间
- 合理安排数据位置
- 避免信息重叠

### 4.2 可读性优化
- 重要数据(角度)放在显眼位置
- 使用一致的数据格式
- 保持布局整齐对齐

### 4.3 实时性优化
- 10Hz刷新率保证流畅显示
- 中断接收确保数据实时性
- 最小化显示延迟

## 5. 代码实现

### 5.1 显示函数调用
```c
// 第1行: 标题和状态
OLED_ShowStr(0, 0, "JY901S", 8);
OLED_ShowStr(48, 0, "RX:", 8);
OLED_ShowNum(72, 0, rx_count % 10000, 4, 8);

// 第2行: Roll和Pitch
OLED_ShowStr(0, 1, "R:", 8);
OLED_ShowNum(16, 1, (int)sensor_data.angle[0], 3, 8);
OLED_ShowStr(64, 1, "P:", 8);
OLED_ShowNum(80, 1, (int)sensor_data.angle[1], 3, 8);

// 第3行: Yaw和X轴加速度
OLED_ShowStr(0, 2, "Y:", 8);
OLED_ShowNum(16, 2, (int)sensor_data.angle[2], 3, 8);
OLED_ShowStr(64, 2, "AX:", 8);
OLED_ShowNum(88, 2, (int)(sensor_data.acc[0] * 10), 3, 8);

// 第4行: Y和Z轴加速度
OLED_ShowStr(0, 3, "AY:", 8);
OLED_ShowNum(24, 3, (int)(sensor_data.acc[1] * 10), 3, 8);
OLED_ShowStr(64, 3, "AZ:", 8);
OLED_ShowNum(88, 3, (int)(sensor_data.acc[2] * 10), 3, 8);
```

### 5.2 数据处理
```c
// 角度数据直接显示整数部分
int roll = (int)sensor_data.angle[0];    // -180 ~ +180
int pitch = (int)sensor_data.angle[1];   // -90 ~ +90
int yaw = (int)sensor_data.angle[2];     // 0 ~ 360

// 加速度数据放大10倍显示
int acc_x = (int)(sensor_data.acc[0] * 10);  // ×10后显示
int acc_y = (int)(sensor_data.acc[1] * 10);
int acc_z = (int)(sensor_data.acc[2] * 10);
```

## 6. 使用说明

### 6.1 正常显示状态
```
JY901S        RX:1234
R: 12    P: 34
Y:156    AX: 10
AY: -5    AZ: 98
```

### 6.2 数据含义解读
- **R:12**: 横滚角12°
- **P:34**: 俯仰角34°
- **Y:156**: 偏航角156°
- **AX:10**: X轴加速度1.0g
- **AY:-5**: Y轴加速度-0.5g
- **AZ:98**: Z轴加速度9.8g (重力加速度)
- **RX:1234**: 已接收1234字节数据

### 6.3 故障诊断
- **RX计数不变**: UART接收异常，检查硬件连接
- **角度数据为0**: 数据解析异常，检查传感器状态
- **数据跳变**: 通信干扰，检查电源和接地

## 7. 扩展功能

### 7.1 可选显示模式
如需要，可以实现多页显示模式：
- **页面1**: 角度数据 + 状态信息
- **页面2**: 加速度 + 角速度数据
- **页面3**: 系统状态 + 调试信息

### 7.2 动态显示
- 数据变化时高亮显示
- 异常数据闪烁提醒
- 连接状态指示

## 8. 性能指标

### 8.1 显示性能
- **刷新率**: 10Hz
- **显示延迟**: <10ms
- **数据精度**: 角度1°，加速度0.1g

### 8.2 资源占用
- **显示缓冲**: 512字节 (128×32÷8)
- **处理时间**: <5ms/帧
- **CPU占用**: <2%

## 9. 测试验证

### 9.1 显示测试
- [x] 所有文字正确显示
- [x] 数字格式正确
- [x] 布局无重叠
- [x] 刷新流畅

### 9.2 功能测试
- [ ] 角度数据实时更新
- [ ] 加速度数据正确显示
- [ ] RX计数器正常增长
- [ ] 传感器响应正常

---

**设计版本**: v1.0
**适用屏幕**: 0.91寸 128×32 OLED
**创建时间**: 2025-07-28
**设计师**: Emma (产品经理)
