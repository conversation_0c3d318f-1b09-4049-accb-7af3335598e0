#include "mpu6050.h"
#include "MPU6050_Reg.h"

#define MPU6050_ADDRESS_0       0xD0            // MPU6050的I2C从机地址 (AD0=0)
#define MPU6050_ADDRESS_1       0xD2            // MPU6050的I2C从机地址 (AD0=1)

static uint8_t MPU6050_ADDRESS = MPU6050_ADDRESS_0;  // 当前使用的地址

/**
  * 函  数：MPU6050写寄存器
  * 参  数：RegAddress 寄存器地址，范围：参考MPU6050手册的寄存器描述
  * 参  数：Data 要写入寄存器的数据，范围：0x00~0xFF
  * 返 回 值：无
  */
void MPU6050_WriteReg(uint8_t RegAddress, uint8_t Data)
{
    HAL_I2C_Mem_Write(&hi2c1, MPU6050_ADDRESS, RegAddress, I2C_MEMADD_SIZE_8BIT, &Data, 1, 1000);
}

/**
  * 函  数：MPU6050读寄存器
  * 参  数：RegAddress 寄存器地址，范围：参考MPU6050手册的寄存器描述
  * 返 回 值：读取寄存器的数据，范围：0x00~0xFF
  */
uint8_t MPU6050_ReadReg(uint8_t RegAddress)
{
    uint8_t Data;
    HAL_I2C_Mem_Read(&hi2c1, MPU6050_ADDRESS, RegAddress, I2C_MEMADD_SIZE_8BIT, &Data, 1, 1000);
    return Data;
}

/**
  * 函  数：MPU6050检查连接
  * 参  数：无
  * 返 回 值：1-连接成功，0-连接失败
  */
uint8_t MPU6050_CheckConnection(void)
{
    // 尝试第一个地址
    MPU6050_ADDRESS = MPU6050_ADDRESS_0;
    if (HAL_I2C_IsDeviceReady(&hi2c1, MPU6050_ADDRESS, 3, 1000) == HAL_OK)
    {
        return 1;
    }

    // 尝试第二个地址
    MPU6050_ADDRESS = MPU6050_ADDRESS_1;
    if (HAL_I2C_IsDeviceReady(&hi2c1, MPU6050_ADDRESS, 3, 1000) == HAL_OK)
    {
        return 1;
    }

    return 0;  // 两个地址都无法连接
}

/**
  * 函  数：MPU6050初始化
  * 参  数：无
  * 返 回 值：无
  */
void MPU6050_Init(void)
{
    MPU6050_WriteReg(MPU6050_PWR_MGMT_1, 0x01);     // 电源管理寄存器1，取消睡眠模式，选择时钟源为X轴陀螺仪
    MPU6050_WriteReg(MPU6050_PWR_MGMT_2, 0x00);     // 电源管理寄存器2，保持默认值0，所有轴均不待机
    MPU6050_WriteReg(MPU6050_SMPLRT_DIV, 0x09);     // 采样率分频寄存器，配置采样率
    MPU6050_WriteReg(MPU6050_CONFIG, 0x06);         // 配置寄存器，配置DLPF
    MPU6050_WriteReg(MPU6050_GYRO_CONFIG, 0x18);    // 陀螺仪配置寄存器，选择满量程为±2000°/s
    MPU6050_WriteReg(MPU6050_ACCEL_CONFIG, 0x18);   // 加速度计配置寄存器，选择满量程为±16g
}

/**
  * 函  数：MPU6050获取ID号
  * 参  数：无
  * 返 回 值：MPU6050的ID号
  */
uint8_t MPU6050_GetID(void)
{
    return MPU6050_ReadReg(MPU6050_WHO_AM_I);       // 返回WHO_AM_I寄存器的值
}

/**
  * 函  数：MPU6050获取数据
  * 参  数：AccX AccY AccZ 加速度计X、Y、Z轴的数据，使用输出参数的形式返回，范围：-32768~32767
  * 参  数：GyroX GyroY GyroZ 陀螺仪X、Y、Z轴的数据，使用输出参数的形式返回，范围：-32768~32767
  * 返 回 值：无
  */
void MPU6050_GetData(int16_t *AccX, int16_t *AccY, int16_t *AccZ, 
                     int16_t *GyroX, int16_t *GyroY, int16_t *GyroZ)
{
    uint8_t DataH, DataL;                                               // 定义数据的高8位和低8位的变量
    
    DataH = MPU6050_ReadReg(MPU6050_ACCEL_XOUT_H);          // 读取加速度计X轴的高8位数据
    DataL = MPU6050_ReadReg(MPU6050_ACCEL_XOUT_L);          // 读取加速度计X轴的低8位数据
    *AccX = (DataH << 8) | DataL;                           // 数据拼接，通过输出参数返回

    DataH = MPU6050_ReadReg(MPU6050_ACCEL_YOUT_H);          // 读取加速度计Y轴的高8位数据
    DataL = MPU6050_ReadReg(MPU6050_ACCEL_YOUT_L);          // 读取加速度计Y轴的低8位数据
    *AccY = (DataH << 8) | DataL;                           // 数据拼接，通过输出参数返回

    DataH = MPU6050_ReadReg(MPU6050_ACCEL_ZOUT_H);          // 读取加速度计Z轴的高8位数据
    DataL = MPU6050_ReadReg(MPU6050_ACCEL_ZOUT_L);          // 读取加速度计Z轴的低8位数据
    *AccZ = (DataH << 8) | DataL;                           // 数据拼接，通过输出参数返回

    DataH = MPU6050_ReadReg(MPU6050_GYRO_XOUT_H);           // 读取陀螺仪X轴的高8位数据
    DataL = MPU6050_ReadReg(MPU6050_GYRO_XOUT_L);           // 读取陀螺仪X轴的低8位数据
    *GyroX = (DataH << 8) | DataL;                          // 数据拼接，通过输出参数返回

    DataH = MPU6050_ReadReg(MPU6050_GYRO_YOUT_H);           // 读取陀螺仪Y轴的高8位数据
    DataL = MPU6050_ReadReg(MPU6050_GYRO_YOUT_L);           // 读取陀螺仪Y轴的低8位数据
    *GyroY = (DataH << 8) | DataL;                          // 数据拼接，通过输出参数返回

    DataH = MPU6050_ReadReg(MPU6050_GYRO_ZOUT_H);           // 读取陀螺仪Z轴的高8位数据
    DataL = MPU6050_ReadReg(MPU6050_GYRO_ZOUT_L);           // 读取陀螺仪Z轴的低8位数据
    *GyroZ = (DataH << 8) | DataL;                          // 数据拼接，通过输出参数返回
}
