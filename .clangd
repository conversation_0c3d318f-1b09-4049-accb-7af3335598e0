CompileFlags:
    Add:
        - -I"c:\Keil_v5\ARM\ARMCC\include"
        - -I"c:\Keil_v5\ARM\ARMCC\include\rw"
        - -D"__STDC__=1"
        - -D"__STDC_VERSION__=199901L"
        - -D"__STDC_HOSTED__=1"
        - -D"__STDC_ISO_10646__=200607"
        - -D"__EDG__=1"
        - -D"__EDG_VERSION__=407"
        - -D"__EDG_SIZE_TYPE__=unsigned int"
        - -D"__EDG_PTRDIFF_TYPE__=int"
        - -D"__sizeof_int=4"
        - -D"__sizeof_long=4"
        - -D"__sizeof_ptr=4"
        - -D"__ARMCC_VERSION=5060960"
        - -D"__TARGET_CPU_CORTEX_M4_FP=1"
        - -D"__TARGET_FPU_VFPV4_SP_D16=1"
        - -D"__CC_ARM=1"
        - -D"__arm=1"
        - -D"__arm__=1"
        - -D"__TARGET_ARCH_7E_M=1"
        - -D"__TARGET_ARCH_ARM=0"
        - -D"__TARGET_ARCH_THUMB=4"
        - -D"__TARGET_ARCH_A64=0"
        - -D"__TARGET_ARCH_AARCH32=1"
        - -D"__TARGET_PROFILE_M=1"
        - -D"__TARGET_FEATURE_HALFWORD=1"
        - -D"__TARGET_FEATURE_THUMB=1"
        - -D"__TARGET_FEATURE_MULTIPLY=1"
        - -D"__TARGET_FEATURE_DSPMUL=1"
        - -D"__TARGET_FEATURE_DOUBLEWORD=1"
        - -D"__TARGET_FEATURE_DIVIDE=1"
        - -D"__TARGET_FEATURE_UNALIGNED=1"
        - -D"__TARGET_FEATURE_CLZ=1"
        - -D"__TARGET_FEATURE_DMB=1"
        - -D"__TARGET_FPU_VFPV4=1"
        - -D"__TARGET_FPU_VFP=1"
        - -D"__TARGET_FPU_VFP_SINGLE=1"
        - -D"__TARGET_FEATURE_EXTENSION_REGISTER_COUNT=16"
        - -D"__APCS_INTERWORK=1"
        - -D"__FP_FAST_FMAF=1"
        - -D"__thumb=1"
        - -D"__thumb__=1"
        - -D"__t32__=1"
        - -D"__OPTIMISE_SPACE=1"
        - -D"__OPT_SMALL_ASSERT=1"
        - -D"__OPTIMISE_LEVEL=2"
    Remove: []
    CompilationDatabase: ./build/F407 base
Diagnostics:
    Suppress: "*"
