/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : grayscale_sensor_test.c
  * @brief          : 灰度传感器测试程序
  * <AUTHOR> 米醋电子工作室技术团队
  * @version        : V1.0
  * @date           : 2025-07-29
  ******************************************************************************
  * @attention
  *
  * 功能描述：
  * 1. 测试灰度传感器的ADC读取功能
  * 2. 通过串口输出灰度传感器数值
  * 3. 可选择性集成OLED显示功能
  * 4. 提供完整的灰度传感器测试框架
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "adc.h"
#include "dma.h"
#include "i2c.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include <stdio.h>
#include <string.h>

/* printf重定向到UART6 (调试串口) */
#ifdef __GNUC__
#define PUTCHAR_PROTOTYPE int __io_putchar(int ch)
#else
#define PUTCHAR_PROTOTYPE int fputc(int ch, FILE *f)
#endif

PUTCHAR_PROTOTYPE
{
    HAL_UART_Transmit(&huart6, (uint8_t *)&ch, 1, HAL_MAX_DELAY);
    return ch;
}

/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* 灰度传感器数据结构 */
typedef struct {
    uint16_t raw_value;      // ADC原始值 (0-4095)
    float voltage;           // 电压值 (0-3.3V)
    uint8_t grayscale;       // 灰度值 (0-255)
    uint32_t timestamp;      // 时间戳
} GrayscaleSensor_t;

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* 灰度传感器配置参数 */
#define GRAYSCALE_ADC_CHANNEL       ADC_CHANNEL_0    // ADC通道 (PA0)
#define GRAYSCALE_SAMPLE_TIME       ADC_SAMPLETIME_15CYCLES
#define GRAYSCALE_VREF              3.3f             // 参考电压
#define GRAYSCALE_ADC_RESOLUTION    4096             // 12位ADC分辨率

/* 测试配置参数 */
#define TEST_SAMPLE_INTERVAL        100              // 采样间隔 (ms)
#define TEST_DISPLAY_INTERVAL       500              // 显示间隔 (ms)
#define TEST_CALIBRATION_SAMPLES    100              // 校准采样次数

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
ADC_HandleTypeDef hadc1;
DMA_HandleTypeDef hdma_adc1;

/* USER CODE BEGIN PV */

/* 灰度传感器实例 */
GrayscaleSensor_t grayscale_sensor;

/* 测试控制变量 */
uint32_t last_sample_time = 0;
uint32_t last_display_time = 0;
uint32_t sample_counter = 0;

/* 校准参数 */
uint16_t calibration_min = 4095;
uint16_t calibration_max = 0;
bool calibration_mode = false;

/* 统计数据 */
uint32_t total_samples = 0;
uint32_t min_value = 4095;
uint32_t max_value = 0;
float average_value = 0.0f;

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
static void MX_GPIO_Init(void);
static void MX_DMA_Init(void);
static void MX_ADC1_Init(void);
static void MX_USART6_UART_Init(void);
static void MX_I2C1_Init(void);

/* USER CODE BEGIN PFP */

/* 灰度传感器测试函数 */
void GrayscaleSensor_Init(void);
void GrayscaleSensor_ReadValue(void);
void GrayscaleSensor_ProcessData(void);
void GrayscaleSensor_DisplayResults(void);
void GrayscaleSensor_Calibrate(void);
void GrayscaleSensor_StartCalibration(void);
void GrayscaleSensor_StopCalibration(void);

/* 测试主循环 */
void Test_MainLoop(void);
void Test_PrintHeader(void);
void Test_PrintStatistics(void);

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/**
 * @brief 灰度传感器初始化
 */
void GrayscaleSensor_Init(void)
{
    printf("\r\n=== 灰度传感器测试程序 ===\r\n");
    printf("版本: V1.0\r\n");
    printf("作者: 米醋电子工作室\r\n");
    printf("日期: 2025-07-29\r\n");
    printf("========================\r\n");
    
    /* 初始化灰度传感器数据结构 */
    memset(&grayscale_sensor, 0, sizeof(GrayscaleSensor_t));
    
    /* 启动ADC */
    if (HAL_ADC_Start(&hadc1) != HAL_OK) {
        printf("错误: ADC启动失败\r\n");
        Error_Handler();
    }
    
    printf("灰度传感器初始化完成\r\n");
    printf("ADC通道: %d\r\n", GRAYSCALE_ADC_CHANNEL);
    printf("采样间隔: %d ms\r\n", TEST_SAMPLE_INTERVAL);
    printf("显示间隔: %d ms\r\n", TEST_DISPLAY_INTERVAL);
    printf("========================\r\n");
}

/**
 * @brief 读取灰度传感器数值
 */
void GrayscaleSensor_ReadValue(void)
{
    /* 启动ADC转换 */
    if (HAL_ADC_PollForConversion(&hadc1, HAL_MAX_DELAY) == HAL_OK) {
        /* 读取ADC值 */
        grayscale_sensor.raw_value = HAL_ADC_GetValue(&hadc1);
        grayscale_sensor.timestamp = HAL_GetTick();
        
        /* 计算电压值 */
        grayscale_sensor.voltage = (float)grayscale_sensor.raw_value * GRAYSCALE_VREF / GRAYSCALE_ADC_RESOLUTION;
        
        /* 计算灰度值 (0-255) */
        if (calibration_max > calibration_min) {
            // 使用校准值计算
            if (grayscale_sensor.raw_value <= calibration_min) {
                grayscale_sensor.grayscale = 0;
            } else if (grayscale_sensor.raw_value >= calibration_max) {
                grayscale_sensor.grayscale = 255;
            } else {
                grayscale_sensor.grayscale = (uint8_t)((grayscale_sensor.raw_value - calibration_min) * 255 / (calibration_max - calibration_min));
            }
        } else {
            // 直接映射
            grayscale_sensor.grayscale = (uint8_t)(grayscale_sensor.raw_value * 255 / GRAYSCALE_ADC_RESOLUTION);
        }
        
        sample_counter++;
        total_samples++;
    }
}

/**
 * @brief 处理灰度传感器数据
 */
void GrayscaleSensor_ProcessData(void)
{
    /* 更新统计数据 */
    if (grayscale_sensor.raw_value < min_value) {
        min_value = grayscale_sensor.raw_value;
    }
    if (grayscale_sensor.raw_value > max_value) {
        max_value = grayscale_sensor.raw_value;
    }
    
    /* 计算平均值 */
    average_value = (average_value * (total_samples - 1) + grayscale_sensor.raw_value) / total_samples;
    
    /* 校准模式处理 */
    if (calibration_mode) {
        if (grayscale_sensor.raw_value < calibration_min) {
            calibration_min = grayscale_sensor.raw_value;
        }
        if (grayscale_sensor.raw_value > calibration_max) {
            calibration_max = grayscale_sensor.raw_value;
        }
    }
}

/**
 * @brief 显示测试结果
 */
void GrayscaleSensor_DisplayResults(void)
{
    printf("时间: %6lu | ADC: %4d | 电压: %1.3fV | 灰度: %3d | 样本: %lu\r\n",
           grayscale_sensor.timestamp,
           grayscale_sensor.raw_value,
           grayscale_sensor.voltage,
           grayscale_sensor.grayscale,
           total_samples);
    
    /* 校准模式显示 */
    if (calibration_mode) {
        printf("校准中... 最小值: %d, 最大值: %d\r\n", calibration_min, calibration_max);
    }
}

/**
 * @brief 开始校准
 */
void GrayscaleSensor_StartCalibration(void)
{
    calibration_mode = true;
    calibration_min = 4095;
    calibration_max = 0;
    printf("\r\n=== 开始校准模式 ===\r\n");
    printf("请将传感器放在最亮和最暗的表面上移动...\r\n");
}

/**
 * @brief 停止校准
 */
void GrayscaleSensor_StopCalibration(void)
{
    calibration_mode = false;
    printf("\r\n=== 校准完成 ===\r\n");
    printf("校准范围: %d - %d\r\n", calibration_min, calibration_max);
    printf("校准范围电压: %.3fV - %.3fV\r\n", 
           (float)calibration_min * GRAYSCALE_VREF / GRAYSCALE_ADC_RESOLUTION,
           (float)calibration_max * GRAYSCALE_VREF / GRAYSCALE_ADC_RESOLUTION);
}

/**
 * @brief 打印测试头部信息
 */
void Test_PrintHeader(void)
{
    printf("\r\n=== 开始灰度传感器测试 ===\r\n");
    printf("格式: 时间 | ADC值 | 电压 | 灰度值 | 样本数\r\n");
    printf("提示: 发送 'c' 开始校准, 's' 停止校准, 'r' 重置统计\r\n");
    printf("=====================================\r\n");
}

/**
 * @brief 打印统计信息
 */
void Test_PrintStatistics(void)
{
    printf("\r\n=== 统计信息 ===\r\n");
    printf("总样本数: %lu\r\n", total_samples);
    printf("最小值: %d (%.3fV)\r\n", min_value, (float)min_value * GRAYSCALE_VREF / GRAYSCALE_ADC_RESOLUTION);
    printf("最大值: %d (%.3fV)\r\n", max_value, (float)max_value * GRAYSCALE_VREF / GRAYSCALE_ADC_RESOLUTION);
    printf("平均值: %.1f (%.3fV)\r\n", average_value, average_value * GRAYSCALE_VREF / GRAYSCALE_ADC_RESOLUTION);
    printf("动态范围: %d\r\n", max_value - min_value);
    printf("===============\r\n");
}

/**
 * @brief 测试主循环
 */
void Test_MainLoop(void)
{
    uint32_t current_time = HAL_GetTick();
    
    /* 定时采样 */
    if (current_time - last_sample_time >= TEST_SAMPLE_INTERVAL) {
        GrayscaleSensor_ReadValue();
        GrayscaleSensor_ProcessData();
        last_sample_time = current_time;
    }
    
    /* 定时显示 */
    if (current_time - last_display_time >= TEST_DISPLAY_INTERVAL) {
        GrayscaleSensor_DisplayResults();
        last_display_time = current_time;
    }
    
    /* 每10秒打印一次统计信息 */
    if (total_samples > 0 && total_samples % 100 == 0 && sample_counter >= 100) {
        Test_PrintStatistics();
        sample_counter = 0;
    }
}

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{
  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_ADC1_Init();
  MX_USART6_UART_Init();
  MX_I2C1_Init();
  /* USER CODE BEGIN 2 */

  /* 灰度传感器测试初始化 */
  GrayscaleSensor_Init();
  Test_PrintHeader();

  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* 测试主循环 */
    Test_MainLoop();
    
    /* 短暂延时 */
    HAL_Delay(1);

    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
  }
  /* USER CODE END 3 */
}
