/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : I2C灰度传感器+OLED显示测试程序 【已修复编译错误】
  * <AUTHOR> 米醋电子工作室技术团队
  * @version        : V1.1 - 清理版本
  * @date           : 2025-07-29 18:16 【文件已更新】
  ******************************************************************************
  * @attention
  *
  * 功能描述：
  * 1. 通过I2C接口读取灰度传感器数字量
  * 2. 通过串口输出灰度传感器数值
  * 3. 实时监控灰度传感器状态
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "i2c.h"
#include "dma.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include <stdio.h>
#include <string.h>
#include "../../Moudle/oled/oled.h"

/* printf重定向到UART6 */
#ifdef __GNUC__
#define PUTCHAR_PROTOTYPE int __io_putchar(int ch)
#else
#define PUTCHAR_PROTOTYPE int fputc(int ch, FILE *f)
#endif

PUTCHAR_PROTOTYPE
{
    HAL_UART_Transmit(&huart6, (uint8_t *)&ch, 1, HAL_MAX_DELAY);
    return ch;
}
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
I2C_HandleTypeDef hi2c1;

/* USER CODE BEGIN PV */

/* I2C灰度传感器配置 */
#define GRAYSCALE_SENSOR_ADDR    0x48    // 灰度传感器I2C地址 (7位地址)
#define GRAYSCALE_REG_DATA       0x00    // 数据寄存器
#define GRAYSCALE_REG_CONFIG     0x01    // 配置寄存器

/* 灰度传感器变量 */
uint8_t sensor_data[2] = {0};           // I2C接收缓冲区
uint16_t grayscale_value = 0;           // 灰度数字量
uint32_t sample_count = 0;              // 采样计数
HAL_StatusTypeDef i2c_status;           // I2C状态

/* OLED显示变量 */
char oled_buffer[32];                   // OLED显示缓冲区
uint32_t last_oled_update = 0;          // 上次OLED更新时间

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
static void MX_GPIO_Init(void);
static void MX_DMA_Init(void);
static void MX_I2C1_Init(void);
static void MX_USART6_UART_Init(void);

/* USER CODE BEGIN PFP */

void GrayscaleSensor_I2C_Test(void);
HAL_StatusTypeDef GrayscaleSensor_I2C_Read(uint16_t *value);
void GrayscaleSensor_I2C_Init(void);
void OLED_Display_Init(void);
void OLED_Display_Update(void);

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/**
 * @brief I2C灰度传感器初始化
 */
void GrayscaleSensor_I2C_Init(void)
{
    printf("I2C灰度传感器初始化...\r\n");

    /* 检查I2C设备是否存在 */
    if (HAL_I2C_IsDeviceReady(&hi2c1, GRAYSCALE_SENSOR_ADDR << 1, 3, 1000) == HAL_OK) {
        printf("I2C灰度传感器检测成功 (地址: 0x%02X)\r\n", GRAYSCALE_SENSOR_ADDR);
    } else {
        printf("警告: I2C灰度传感器未检测到\r\n");
    }
}

/**
 * @brief 读取I2C灰度传感器数据
 * @param value: 输出的灰度值指针
 * @retval HAL状态
 */
HAL_StatusTypeDef GrayscaleSensor_I2C_Read(uint16_t *value)
{
    HAL_StatusTypeDef status;

    /* 读取2字节数据 */
    status = HAL_I2C_Mem_Read(&hi2c1, GRAYSCALE_SENSOR_ADDR << 1,
                              GRAYSCALE_REG_DATA, I2C_MEMADD_SIZE_8BIT,
                              sensor_data, 2, 1000);

    if (status == HAL_OK) {
        /* 组合16位数据 (高字节在前) */
        *value = (sensor_data[0] << 8) | sensor_data[1];
    }

    return status;
}

/**
 * @brief OLED显示初始化
 */
void OLED_Display_Init(void)
{
    printf("OLED显示器初始化...\r\n");

    /* 初始化OLED */
    OLED_Init();
    OLED_Clear();

    /* 显示标题 */
    OLED_ShowStr(0, 0, "I2C Grayscale", 16);
    OLED_ShowStr(0, 2, "Sensor Test", 16);
    OLED_ShowStr(0, 4, "Initializing...", 12);

    HAL_Delay(2000);  // 显示2秒初始化信息
    OLED_Clear();

    printf("OLED显示器初始化完成\r\n");
}

/**
 * @brief OLED显示更新
 */
void OLED_Display_Update(void)
{
    /* 第1行：标题 */
    OLED_ShowStr(0, 0, "I2C Grayscale", 16);

    /* 第2行：I2C状态 */
    if (i2c_status == HAL_OK) {
        OLED_ShowStr(0, 2, "Status: OK", 12);
    } else {
        snprintf(oled_buffer, sizeof(oled_buffer), "Status: ERR%d", i2c_status);
        OLED_ShowStr(0, 2, oled_buffer, 12);
    }

    /* 第3行：灰度值 */
    snprintf(oled_buffer, sizeof(oled_buffer), "Value: %5d", grayscale_value);
    OLED_ShowStr(0, 4, oled_buffer, 12);

    /* 第4行：十六进制值 */
    snprintf(oled_buffer, sizeof(oled_buffer), "Hex: 0x%04X", grayscale_value);
    OLED_ShowStr(0, 6, oled_buffer, 12);
}

/**
 * @brief I2C灰度传感器测试函数
 */
void GrayscaleSensor_I2C_Test(void)
{
    /* 读取I2C灰度传感器数据 */
    i2c_status = GrayscaleSensor_I2C_Read(&grayscale_value);

    if (i2c_status == HAL_OK) {
        /* 增加样本计数 */
        sample_count++;

        /* 输出结果 */
        printf("样本: %lu | I2C状态: OK | 灰度值: %5d (0x%04X)\r\n",
               sample_count, grayscale_value, grayscale_value);
    } else {
        /* I2C读取失败 */
        printf("样本: %lu | I2C状态: ERROR | 错误代码: %d\r\n",
               sample_count, i2c_status);
        sample_count++;
    }

    /* 更新OLED显示 */
    uint32_t current_time = HAL_GetTick();
    if (current_time - last_oled_update >= 200) {  // 每200ms更新一次OLED
        OLED_Display_Update();
        last_oled_update = current_time;
    }
}

/* USER CODE END 0 */



/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{
  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_I2C1_Init();
  MX_USART6_UART_Init();

  /* USER CODE BEGIN 2 */

  printf("\r\n=== I2C灰度传感器+OLED测试程序 ===\r\n");
  printf("版本: V1.1 - 清理版本\r\n");
  printf("作者: 米醋电子工作室\r\n");
  printf("【编译错误已修复 - 文件已更新】\r\n");
  printf("==================================\r\n");

  /* OLED显示器初始化 */
  OLED_Display_Init();

  /* I2C灰度传感器初始化 */
  GrayscaleSensor_I2C_Init();

  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* I2C灰度传感器测试 */
    GrayscaleSensor_I2C_Test();

    /* 延时500ms */
    HAL_Delay(500);

    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_RCC_PWR_CLK_ENABLE();
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSI;
  RCC_OscInitStruct.PLL.PLLM = 8;
  RCC_OscInitStruct.PLL.PLLN = 180;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = 4;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Activate the Over-Drive mode
  */
  if (HAL_PWREx_EnableOverDrive() != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_5) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/**
 * @brief GPIO初始化
 */
static void MX_GPIO_Init(void)
{
  /* GPIO Ports Clock Enable */
  __HAL_RCC_GPIOA_CLK_ENABLE();
  __HAL_RCC_GPIOB_CLK_ENABLE();
  __HAL_RCC_GPIOC_CLK_ENABLE();
  __HAL_RCC_GPIOD_CLK_ENABLE();
  __HAL_RCC_GPIOE_CLK_ENABLE();
}

/**
 * @brief DMA初始化
 */
static void MX_DMA_Init(void)
{
  /* DMA controller clock enable */
  __HAL_RCC_DMA1_CLK_ENABLE();
  __HAL_RCC_DMA2_CLK_ENABLE();
}

/**
 * @brief I2C1初始化
 */
static void MX_I2C1_Init(void)
{
  /* I2C1 clock enable */
  __HAL_RCC_I2C1_CLK_ENABLE();
}

/**
 * @brief I2C2初始化
 */
static void MX_I2C2_Init(void)
{
  /* I2C2 clock enable */
  __HAL_RCC_I2C2_CLK_ENABLE();
}

/**
 * @brief TIM1初始化
 */
static void MX_TIM1_Init(void)
{
  /* TIM1 clock enable */
  __HAL_RCC_TIM1_CLK_ENABLE();
}

/**
 * @brief UART4初始化 (EMMV5 Y轴电机)
 */
static void MX_UART4_Init(void)
{
  huart4.Instance = UART4;
  huart4.Init.BaudRate = 9600;
  huart4.Init.WordLength = UART_WORDLENGTH_8B;
  huart4.Init.StopBits = UART_STOPBITS_1;
  huart4.Init.Parity = UART_PARITY_NONE;
  huart4.Init.Mode = UART_MODE_TX_RX;
  huart4.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart4.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart4) != HAL_OK) {
    Error_Handler();
  }
}

/**
 * @brief UART5初始化 (JY901S姿态传感器)
 */
static void MX_UART5_Init(void)
{
  huart5.Instance = UART5;
  huart5.Init.BaudRate = 9600;
  huart5.Init.WordLength = UART_WORDLENGTH_8B;
  huart5.Init.StopBits = UART_STOPBITS_1;
  huart5.Init.Parity = UART_PARITY_NONE;
  huart5.Init.Mode = UART_MODE_TX_RX;
  huart5.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart5.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart5) != HAL_OK) {
    Error_Handler();
  }
}

/**
 * @brief USART2初始化 (EMMV5 X轴电机)
 */
static void MX_USART2_UART_Init(void)
{
  huart2.Instance = USART2;
  huart2.Init.BaudRate = 9600;
  huart2.Init.WordLength = UART_WORDLENGTH_8B;
  huart2.Init.StopBits = UART_STOPBITS_1;
  huart2.Init.Parity = UART_PARITY_NONE;
  huart2.Init.Mode = UART_MODE_TX_RX;
  huart2.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart2.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart2) != HAL_OK) {
    Error_Handler();
  }
}

/**
 * @brief USART6初始化 (K230D数据接收)
 */
static void MX_USART6_UART_Init(void)
{
  huart6.Instance = USART6;
  huart6.Init.BaudRate = 115200;
  huart6.Init.WordLength = UART_WORDLENGTH_8B;
  huart6.Init.StopBits = UART_STOPBITS_1;
  huart6.Init.Parity = UART_PARITY_NONE;
  huart6.Init.Mode = UART_MODE_TX_RX;
  huart6.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart6.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart6) != HAL_OK) {
    Error_Handler();
  }
}

/**
 * @brief UART MSP初始化
 */
void HAL_UART_MspInit(UART_HandleTypeDef* uartHandle)
{
  GPIO_InitTypeDef GPIO_InitStruct = {0};

  if(uartHandle->Instance==UART4)
  {
    /* UART4 clock enable */
    __HAL_RCC_UART4_CLK_ENABLE();
    __HAL_RCC_GPIOC_CLK_ENABLE();

    /* UART4 GPIO Configuration: PC10->TX, PC11->RX */
    GPIO_InitStruct.Pin = GPIO_PIN_10|GPIO_PIN_11;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF8_UART4;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
  }
  else if(uartHandle->Instance==UART5)
  {
    /* UART5 clock enable */
    __HAL_RCC_UART5_CLK_ENABLE();
    __HAL_RCC_GPIOC_CLK_ENABLE();
    __HAL_RCC_GPIOD_CLK_ENABLE();

    /* UART5 GPIO Configuration: PC12->TX, PD2->RX */
    GPIO_InitStruct.Pin = GPIO_PIN_12;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF8_UART5;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);

    GPIO_InitStruct.Pin = GPIO_PIN_2;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF8_UART5;
    HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);
  }
  else if(uartHandle->Instance==USART2)
  {
    /* USART2 clock enable */
    __HAL_RCC_USART2_CLK_ENABLE();
    __HAL_RCC_GPIOA_CLK_ENABLE();

    /* USART2 GPIO Configuration: PA2->TX, PA3->RX */
    GPIO_InitStruct.Pin = GPIO_PIN_2|GPIO_PIN_3;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF7_USART2;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
  }
  else if(uartHandle->Instance==USART6)
  {
    /* USART6 clock enable */
    __HAL_RCC_USART6_CLK_ENABLE();
    __HAL_RCC_GPIOC_CLK_ENABLE();

    /* USART6 GPIO Configuration: PC6->TX, PC7->RX */
    GPIO_InitStruct.Pin = GPIO_PIN_6|GPIO_PIN_7;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF8_USART6;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
  }
}

/**
 * @brief 错误处理函数
 */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  System_ErrorHandler();
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  printf("Assert failed: file %s on line %lu\r\n", file, line);
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */

/* USER CODE END 4 */





/**
  * @brief I2C1 Initialization Function
  * @param None
  * @retval None
  */
static void MX_I2C1_Init(void)
{
  /* USER CODE BEGIN I2C1_Init 0 */

  /* USER CODE END I2C1_Init 0 */

  /* USER CODE BEGIN I2C1_Init 1 */

  /* USER CODE END I2C1_Init 1 */
  hi2c1.Instance = I2C1;
  hi2c1.Init.ClockSpeed = 100000;
  hi2c1.Init.DutyCycle = I2C_DUTYCYCLE_2;
  hi2c1.Init.OwnAddress1 = 0;
  hi2c1.Init.AddressingMode = I2C_ADDRESSINGMODE_7BIT;
  hi2c1.Init.DualAddressMode = I2C_DUALADDRESS_DISABLE;
  hi2c1.Init.OwnAddress2 = 0;
  hi2c1.Init.GeneralCallMode = I2C_GENERALCALL_DISABLE;
  hi2c1.Init.NoStretchMode = I2C_NOSTRETCH_DISABLE;
  if (HAL_I2C_Init(&hi2c1) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN I2C1_Init 2 */

  /* USER CODE END I2C1_Init 2 */
}

/**
  * @brief USART6 Initialization Function
  * @param None
  * @retval None
  */
static void MX_USART6_UART_Init(void)
{
  /* USER CODE BEGIN USART6_Init 0 */

  /* USER CODE END USART6_Init 0 */

  /* USER CODE BEGIN USART6_Init 1 */

  /* USER CODE END USART6_Init 1 */
  huart6.Instance = USART6;
  huart6.Init.BaudRate = 115200;
  huart6.Init.WordLength = UART_WORDLENGTH_8B;
  huart6.Init.StopBits = UART_STOPBITS_1;
  huart6.Init.Parity = UART_PARITY_NONE;
  huart6.Init.Mode = UART_MODE_TX_RX;
  huart6.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart6.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart6) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN USART6_Init 2 */

  /* USER CODE END USART6_Init 2 */
}

/**
  * @brief GPIO Initialization Function
  * @param None
  * @retval None
  */
static void MX_GPIO_Init(void)
{
  /* GPIO Ports Clock Enable */
  __HAL_RCC_GPIOA_CLK_ENABLE();
  __HAL_RCC_GPIOC_CLK_ENABLE();
}

/**
  * @brief DMA Initialization Function
  * @param None
  * @retval None
  */
static void MX_DMA_Init(void)
{
  /* DMA controller clock enable */
  __HAL_RCC_DMA2_CLK_ENABLE();
}

/* USER CODE END 4 */
