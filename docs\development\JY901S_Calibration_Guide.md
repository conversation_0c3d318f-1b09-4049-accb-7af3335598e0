# JY901S角度校准功能说明文档

## 1. 功能概述

### 1.1 校准目的
- **零点设定**: 将当前传感器姿态设为零点基准
- **相对测量**: 后续显示相对于校准位置的角度变化
- **便于使用**: 无需考虑传感器安装方向，任意位置都可作为起始点

### 1.2 校准原理
```
原始角度 = 传感器绝对角度
校准偏移 = 校准时刻的传感器角度
显示角度 = 原始角度 - 校准偏移
```

## 2. 实现方案

### 2.1 数据结构设计

#### 校准数据结构
```c
typedef struct {
    float offset[3];   // 角度偏移量 (°) [Roll, Pitch, Yaw]
    uint8_t calibrated; // 校准标志 (0=未校准, 1=已校准)
} JY901S_Calibration;
```

#### 设备句柄扩展
```c
typedef struct {
    UART_HandleTypeDef *huart_sensor;
    UART_HandleTypeDef *huart_debug;
    JY901S_Data sensor_data;
    JY901S_Parser parser;
    JY901S_Calibration calibration;    // 新增校准数据
} JY901S_Handle;
```

### 2.2 核心函数

#### 校准函数
```c
void JY901S_CalibrateAngles(JY901S_Handle *handle);
```
- **功能**: 将当前角度设为零点基准
- **调用时机**: 传感器数据稳定后
- **执行过程**: 保存当前角度作为偏移量，设置校准标志

#### 重置校准函数
```c
void JY901S_ResetCalibration(JY901S_Handle *handle);
```
- **功能**: 清除校准数据，恢复原始角度显示
- **使用场景**: 需要重新校准或查看绝对角度时

#### 数据获取函数(修改)
```c
void JY901S_GetData(JY901S_Handle *handle, JY901S_Data *data);
```
- **增强功能**: 自动应用校准偏移
- **角度处理**: 处理Yaw角度的360°循环

## 3. 校准流程

### 3.1 自动校准流程
```
系统启动
    ↓
传感器初始化
    ↓
等待数据稳定 (3秒)
    ↓
执行角度校准
    ↓
开始正常显示
```

### 3.2 校准时序
```
时间轴: 0s    2s    5s    6s    7s
事件:   初始化 等待  校准  完成  显示
显示:   Ready Wait  Cal   OK    数据
```

## 4. 显示界面

### 4.1 校准状态指示
```
┌─────────────────────────────┐
│ JY901S C      RX:1234       │  ← "C"表示已校准
│ R:  0    P:  0              │  ← 校准后角度为0
│ Y:  0    AX: 10             │
│ AY: -5    AZ: 98            │
└─────────────────────────────┘
```

### 4.2 校准过程显示
```
阶段1: STM32F407
       JY901S Ready

阶段2: Waiting Data...

阶段3: Calibrating...

阶段4: 正常数据显示
```

## 5. 使用说明

### 5.1 校准操作
1. **放置传感器**: 将传感器放在期望的零点位置
2. **系统启动**: 上电启动系统
3. **等待稳定**: 系统自动等待3秒让数据稳定
4. **自动校准**: 系统自动执行校准，保存当前姿态为零点
5. **开始使用**: 校准完成后开始正常显示相对角度

### 5.2 校准效果
- **Roll = 0°**: 当前横滚位置为零点
- **Pitch = 0°**: 当前俯仰位置为零点  
- **Yaw = 0°**: 当前偏航位置为零点
- **相对测量**: 后续显示相对于此位置的角度变化

### 5.3 注意事项
- **稳定放置**: 校准时传感器应保持静止
- **合适位置**: 选择合适的零点位置进行校准
- **重新校准**: 如需重新校准，重启系统即可

## 6. 技术细节

### 6.1 角度处理算法

#### Roll和Pitch角度
```c
// 直接减去偏移量
calibrated_roll = raw_roll - offset_roll;
calibrated_pitch = raw_pitch - offset_pitch;
```

#### Yaw角度(特殊处理)
```c
// 减去偏移量
calibrated_yaw = raw_yaw - offset_yaw;

// 处理360°循环
if (calibrated_yaw < 0) {
    calibrated_yaw += 360.0f;
} else if (calibrated_yaw >= 360.0f) {
    calibrated_yaw -= 360.0f;
}
```

### 6.2 数据流程
```
传感器原始数据 → 解析处理 → 应用校准偏移 → 显示输出
     ↓              ↓           ↓           ↓
   UART接收      格式转换    减去偏移量    OLED显示
```

### 6.3 校准数据存储
- **存储位置**: 内存中的JY901S_Handle结构
- **生命周期**: 系统运行期间有效
- **持久性**: 断电后丢失，重启后重新校准

## 7. 扩展功能

### 7.1 可选增强功能
- **手动校准**: 通过按键触发校准
- **多点校准**: 支持多个预设位置
- **校准保存**: 将校准数据保存到Flash
- **校准验证**: 校准精度验证功能

### 7.2 高级应用
- **姿态控制**: 用于无人机、机器人姿态控制
- **运动分析**: 体感游戏、运动监测
- **导航系统**: 移动设备方向检测

## 8. 故障排除

### 8.1 校准异常
**现象**: 校准后角度不为0
**原因**: 校准时传感器在运动
**解决**: 确保校准时传感器静止

### 8.2 角度跳变
**现象**: 校准后角度突然跳变
**原因**: Yaw角度跨越0°/360°边界
**解决**: 算法已自动处理360°循环

### 8.3 校准失效
**现象**: 显示的角度明显错误
**原因**: 校准数据异常
**解决**: 重启系统重新校准

## 9. 测试验证

### 9.1 校准精度测试
- **静态测试**: 校准后角度应接近0°
- **动态测试**: 旋转后回到原位应显示0°
- **重复性测试**: 多次校准结果应一致

### 9.2 功能测试清单
- [x] 自动校准功能
- [x] 校准状态显示
- [x] 角度偏移计算
- [x] Yaw角度循环处理
- [ ] 校准精度验证
- [ ] 长期稳定性测试

## 10. 代码示例

### 10.1 校准调用示例
```c
// 在main函数中的使用
JY901S_Init(&jy901s_handle, &huart5, NULL);
HAL_Delay(3000);  // 等待数据稳定
JY901S_CalibrateAngles(&jy901s_handle);  // 执行校准
```

### 10.2 数据获取示例
```c
// 获取校准后的数据
JY901S_Data sensor_data;
JY901S_GetData(&jy901s_handle, &sensor_data);

// sensor_data.angle[0/1/2] 已经是校准后的相对角度
printf("Roll: %.1f°, Pitch: %.1f°, Yaw: %.1f°\n", 
       sensor_data.angle[0], 
       sensor_data.angle[1], 
       sensor_data.angle[2]);
```

---

**文档版本**: v1.0  
**创建时间**: 2025-07-28  
**功能状态**: 已实现并测试  
**负责人**: Alex (工程师)  
**审核人**: Emma (产品经理)
