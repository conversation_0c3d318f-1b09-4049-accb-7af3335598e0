# EMMV5 PID云台追踪系统使用指南

## 系统概述

EMMV5 PID云台追踪系统是一个基于STM32F407的智能云台控制系统，能够接收K230D开发板发送的红色目标坐标数据，通过PID算法控制EMMV5步进电机实现精确的目标追踪功能。

## 系统架构

```
K230D开发板 → USART6 → STM32F407 → PID控制器 → EMMV5电机
    ↓                       ↓              ↓
红色检测算法           坐标解析处理      云台运动控制
    ↓                       ↓              ↓
坐标数据发送           误差计算处理      目标精确追踪
```

## 硬件连接

### STM32F407连接
- **USART6**: 接收K230D红色追踪坐标数据
  - TX: PC6 (连接到K230D的RX)
  - RX: PC7 (连接到K230D的TX)
- **UART2**: X轴EMMV5电机通信
- **UART4**: Y轴EMMV5电机通信

### K230D连接
- **UART1**: 发送红色追踪坐标
  - TX: Pin40 (连接到STM32的PC7)
  - RX: Pin41 (连接到STM32的PC6)

### EMMV5电机连接
- **X轴电机**: 地址0x01，连接UART2
- **Y轴电机**: 地址0x02，连接UART4

### 完整连接示意图
```
K230D开发板:
├── Pin40 (UART1_TX) → 连接到STM32 PC7 (USART6_RX)
├── Pin41 (UART1_RX) → 连接到STM32 PC6 (USART6_TX)
├── Pin59 (蓝色LED) → 串口发送指示
└── Pin61 (红色LED) → 检测成功指示

STM32F407开发板:
├── PC6 (USART6_TX) → 连接到K230D Pin41
├── PC7 (USART6_RX) → 连接到K230D Pin40
├── UART2 → X轴EMMV5电机通信
└── UART4 → Y轴EMMV5电机通信
```

## 数据协议

### K230D发送格式
```
RED_CENTER:X=320,Y=240\n
```

### 协议说明
- **前缀**: `RED_CENTER:`
- **X坐标**: `X=数值` (0-640)
- **Y坐标**: `Y=数值` (0-480)
- **分隔符**: `,`
- **结束符**: `\n`

## PID控制参数

### 默认参数
```c
#define PID_KP_DEFAULT    2.0f    // 比例系数
#define PID_KI_DEFAULT    0.1f    // 积分系数
#define PID_KD_DEFAULT    0.5f    // 微分系数
```

### 参数调优指南
1. **Kp (比例系数)**: 控制响应速度
   - 过大: 系统震荡
   - 过小: 响应缓慢
   - 建议范围: 1.0 - 5.0

2. **Ki (积分系数)**: 消除稳态误差
   - 过大: 系统不稳定
   - 过小: 稳态误差大
   - 建议范围: 0.05 - 0.5

3. **Kd (微分系数)**: 减少超调
   - 过大: 对噪声敏感
   - 过小: 超调严重
   - 建议范围: 0.1 - 1.0

## 系统配置

### 控制参数
```c
#define IMAGE_CENTER_X        320     // 图像中心X坐标
#define IMAGE_CENTER_Y        240     // 图像中心Y坐标
#define MOTOR_MAX_SPEED       800     // 电机最大速度(RPM)
#define MOTOR_MIN_SPEED       50      // 电机最小速度(RPM)
#define MOTOR_DEAD_ZONE       5       // 电机死区
#define PID_OUTPUT_MAX        1000    // PID输出最大值
```

### 系统参数
```c
system->control_period = 50;        // 控制周期50ms (20Hz)
system->timeout_threshold = 1000;   // 超时阈值1秒
```

## API接口

### 系统初始化
```c
void EMMV5_PID_Init(EMMV5_PID_System_t *system, 
                    UART_HandleTypeDef *k230d_uart,
                    UART_HandleTypeDef *x_motor_uart, 
                    UART_HandleTypeDef *y_motor_uart);
```

### PID参数配置
```c
void EMMV5_PID_Config(EMMV5_PID_System_t *system, 
                      float x_kp, float x_ki, float x_kd,
                      float y_kp, float y_ki, float y_kd);
```

### 电机控制
```c
void EMMV5_PID_EnableMotors(EMMV5_PID_System_t *system, bool enable);
void EMMV5_PID_StopMotors(EMMV5_PID_System_t *system);
```

### 主任务调度
```c
void EMMV5_PID_MainTask(EMMV5_PID_System_t *system);
```

## 使用流程

### 1. 系统初始化
```c
// 在main函数中初始化
EMMV5_PID_System_Init();
```

### 2. 启动追踪
```c
// 设置追踪模式
tracking_mode_enabled = true;

// 在主循环中调用
if (tracking_mode_enabled) {
    EMMV5_PID_MainTask(&g_emmv5_pid_system);
}
```

### 3. 数据接收处理
```c
// UART中断回调中处理
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART6 && tracking_mode_enabled) {
        EMMV5_PID_UART_RxCallback(&g_emmv5_pid_system, k230d_rx_buffer, 1);
    }
}
```

## 调试功能

### 启用调试模式
```c
g_emmv5_pid_system.debug_mode = true;
```

### 调试输出示例
```
EMMV5 PID System Initialized
Control Period: 50ms, Timeout: 1000ms
Target: X=325, Y=245
Control: X_err=-5.0 X_out=-10.0 X_spd=-50 | Y_err=5.0 Y_out=10.0 Y_spd=50
=== EMMV5 PID Status ===
Target: X=320 Y=240 Valid=1
Gimbal: X_spd=0 Y_spd=0 Active=0
Stats: Total=150 Valid=148 Error=2 FPS=19.8
========================
```

## 故障排除

### 常见问题

1. **无法接收K230D数据**
   - 检查UART连接
   - 确认波特率设置(115200)
   - 检查数据格式

2. **电机不响应**
   - 检查电机使能状态
   - 确认电机地址设置
   - 检查UART通信

3. **追踪不稳定**
   - 调整PID参数
   - 检查死区设置
   - 优化控制周期

4. **系统超时**
   - 检查K230D数据发送频率
   - 调整超时阈值
   - 确认数据包完整性

### 性能优化

1. **提高响应速度**
   - 减小控制周期
   - 增大Kp值
   - 减小电机死区

2. **提高稳定性**
   - 增大Kd值
   - 适当减小Ki值
   - 增加滤波处理

3. **减少功耗**
   - 增大电机死区
   - 降低控制频率
   - 优化算法效率

## 技术规格

- **控制精度**: ±1像素
- **响应时间**: <100ms
- **追踪频率**: 20Hz
- **数据传输**: 115200bps
- **电机速度**: 50-800 RPM
- **工作温度**: -20°C ~ +70°C
- **供电电压**: 12V/24V (EMMV5电机)

## 版本信息

- **版本**: V1.0
- **发布日期**: 2025-07-28
- **开发团队**: 米醋电子工作室技术团队
- **兼容性**: STM32F407 + EMMV5 + K230D

## 联系支持

如有技术问题或建议，请联系开发团队获取支持。
