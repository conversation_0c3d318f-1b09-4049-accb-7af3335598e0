##########################################################################################
#                        Append Compiler Options For Source Files
##########################################################################################

# syntax:
#   <your pattern>: <compiler options>
# For get pattern syntax, please refer to: https://www.npmjs.com/package/micromatch
#
# examples:
#   'main.cpp':           --cpp11 -Og ...
#   'src/*.c':            -gnu -O2 ...
#   'src/lib/**/*.cpp':   --cpp11 -Os ...
#   '!Application/*.c':   -O0
#   '**/*.c':             -O2 -gnu ...

version: "2.0"
options:
    F407 base:
        files: {}
        virtualPathFiles:
            <virtual_root>/Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_i2c.c: ""
            <virtual_root>/Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_i2c_ex.c: ""
            <virtual_root>/Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_tim.c: ""
            <virtual_root>/Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_tim_ex.c: ""
            <virtual_root>/Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_uart.c: ""
