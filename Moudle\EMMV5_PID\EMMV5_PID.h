#ifndef __EMMV5_PID_H
#define __EMMV5_PID_H

#include "../../Core/Inc/main.h"
#include "../EMM_V5/Emm_V5.h"
#include <stdbool.h>
#include <string.h>
#include <stdio.h>
#include <stdlib.h>

/**********************************************************
*** EMMV5 PID云台追踪系统
*** 编写作者：米醋电子工作室技术团队
*** 版本：V1.0
*** 日期：2025-07-28
*** 功能：接收K230D红色追踪坐标，PID控制EMMV5云台追踪
**********************************************************/

/* 系统配置参数 */
#define EMMV5_PID_UART_TIMEOUT      1000    /* UART超时时间(ms) */
#define EMMV5_PID_BUFFER_SIZE        64      /* 接收缓冲区大小 (优化内存使用) */
#define EMMV5_PID_MAX_DATA_LEN       64      /* 最大数据包长度 */

/* 云台控制参数 */
#define EMMV5_X_MOTOR_ADDR          0x01     /* X轴电机地址 */
#define EMMV5_Y_MOTOR_ADDR          0x02     /* Y轴电机地址 */

/* 图像中心坐标 (K230D摄像头分辨率640x480) */
#define IMAGE_CENTER_X              320      /* 图像中心X坐标 */
#define IMAGE_CENTER_Y              240      /* 图像中心Y坐标 */

/* PID控制参数 */
#define PID_KP_DEFAULT              2.0f     /* 比例系数默认值 */
#define PID_KI_DEFAULT              0.1f     /* 积分系数默认值 */
#define PID_KD_DEFAULT              0.5f     /* 微分系数默认值 */

/* 控制限制参数 */
#define PID_OUTPUT_MAX              1000     /* PID输出最大值 */
#define PID_OUTPUT_MIN              -1000    /* PID输出最小值 */
#define PID_INTEGRAL_MAX            500.0f   /* 积分限幅 */
#define PID_INTEGRAL_MIN            -500.0f  /* 积分限幅 */

/* 电机控制参数 */
#define MOTOR_MAX_SPEED             800      /* 电机最大速度(RPM) */
#define MOTOR_MIN_SPEED             50       /* 电机最小速度(RPM) */
#define MOTOR_ACCELERATION          10       /* 电机加速度 */
#define MOTOR_DEAD_ZONE             5        /* 电机死区 */

/* 数据解析状态 */
typedef enum {
    PARSE_IDLE = 0,         /* 空闲状态 */
    PARSE_HEADER,           /* 解析头部 */
    PARSE_DATA,             /* 解析数据 */
    PARSE_COMPLETE,         /* 解析完成 */
    PARSE_ERROR             /* 解析错误 */
} ParseState_t;

/* PID控制器结构体 */
typedef struct {
    float kp;               /* 比例系数 */
    float ki;               /* 积分系数 */
    float kd;               /* 微分系数 */
    float setpoint;         /* 目标值 */
    float input;            /* 输入值 */
    float output;           /* 输出值 */
    float last_error;       /* 上次误差 */
    float integral;         /* 积分累积 */
    float derivative;       /* 微分值 */
    float error;            /* 当前误差 */
    uint32_t last_time;     /* 上次计算时间 */
    bool first_run;         /* 首次运行标志 */
} PID_Controller_t;

/* 目标坐标结构体 */
typedef struct {
    int16_t x;              /* X坐标 */
    int16_t y;              /* Y坐标 */
    uint32_t timestamp;     /* 时间戳 */
    bool valid;             /* 数据有效标志 */
} TargetCoord_t;

/* 云台状态结构体 */
typedef struct {
    int32_t x_position;     /* X轴当前位置 */
    int32_t y_position;     /* Y轴当前位置 */
    int16_t x_speed;        /* X轴当前速度 */
    int16_t y_speed;        /* Y轴当前速度 */
    bool x_enabled;         /* X轴使能状态 */
    bool y_enabled;         /* Y轴使能状态 */
    bool tracking_active;   /* 追踪激活状态 */
    uint32_t last_update;   /* 最后更新时间 */
} GimbalStatus_t;

/* 系统统计信息 */
typedef struct {
    uint32_t total_packets;     /* 总接收包数 */
    uint32_t valid_packets;     /* 有效包数 */
    uint32_t error_packets;     /* 错误包数 */
    uint32_t tracking_time;     /* 追踪总时间 */
    uint32_t last_packet_time;  /* 最后接收包时间 */
    float average_fps;          /* 平均帧率 */
} SystemStats_t;

/* EMMV5 PID系统主结构体 */
typedef struct {
    /* 硬件接口 */
    UART_HandleTypeDef *k230d_uart;    /* K230D通信串口 */
    UART_HandleTypeDef *x_motor_uart;  /* X轴电机串口 */
    UART_HandleTypeDef *y_motor_uart;  /* Y轴电机串口 */
    
    /* PID控制器 */
    PID_Controller_t x_pid;             /* X轴PID控制器 */
    PID_Controller_t y_pid;             /* Y轴PID控制器 */
    
    /* 数据缓冲区 */
    uint8_t rx_buffer[EMMV5_PID_BUFFER_SIZE];   /* 接收缓冲区 */
    uint16_t rx_index;                          /* 接收索引 */
    ParseState_t parse_state;                   /* 解析状态 */
    
    /* 目标和状态 */
    TargetCoord_t target;               /* 目标坐标 */
    GimbalStatus_t gimbal;              /* 云台状态 */
    SystemStats_t stats;                /* 系统统计 */
    
    /* 控制参数 */
    bool auto_tracking;                 /* 自动追踪开关 */
    bool debug_mode;                    /* 调试模式 */
    uint32_t control_period;            /* 控制周期(ms) */
    uint32_t timeout_threshold;         /* 超时阈值(ms) */
    
} EMMV5_PID_System_t;

/* 函数声明 */

/* 系统初始化和配置 */
void EMMV5_PID_Init(EMMV5_PID_System_t *system, 
                    UART_HandleTypeDef *k230d_uart,
                    UART_HandleTypeDef *x_motor_uart, 
                    UART_HandleTypeDef *y_motor_uart);
void EMMV5_PID_Config(EMMV5_PID_System_t *system, 
                      float x_kp, float x_ki, float x_kd,
                      float y_kp, float y_ki, float y_kd);
void EMMV5_PID_Reset(EMMV5_PID_System_t *system);

/* PID控制器函数 */
void PID_Init(PID_Controller_t *pid, float kp, float ki, float kd);
float PID_Compute(PID_Controller_t *pid, float setpoint, float input);
void PID_Reset(PID_Controller_t *pid);
void PID_SetTunings(PID_Controller_t *pid, float kp, float ki, float kd);

/* 数据接收和解析 */
void EMMV5_PID_UART_RxCallback(EMMV5_PID_System_t *system, uint8_t *data, uint16_t size);
bool EMMV5_PID_ParseCoordinate(EMMV5_PID_System_t *system, char *data_str);
void EMMV5_PID_ProcessReceivedData(EMMV5_PID_System_t *system);

/* 云台控制函数 */
void EMMV5_PID_EnableMotors(EMMV5_PID_System_t *system, bool enable);
void EMMV5_PID_SetTarget(EMMV5_PID_System_t *system, int16_t x, int16_t y);
void EMMV5_PID_UpdateControl(EMMV5_PID_System_t *system);
void EMMV5_PID_StopMotors(EMMV5_PID_System_t *system);

/* 电机控制底层函数 */
void EMMV5_PID_MoveMotor(UART_HandleTypeDef *huart, uint8_t addr, 
                         int16_t speed, uint8_t acceleration);
void EMMV5_PID_ReadMotorStatus(UART_HandleTypeDef *huart, uint8_t addr);

/* 系统状态和调试 */
void EMMV5_PID_UpdateStats(EMMV5_PID_System_t *system);
void EMMV5_PID_PrintStatus(EMMV5_PID_System_t *system);
void EMMV5_PID_PrintDebugInfo(EMMV5_PID_System_t *system);

/* 主循环和任务调度 */
void EMMV5_PID_MainTask(EMMV5_PID_System_t *system);
void EMMV5_PID_PeriodicTask(EMMV5_PID_System_t *system);

/* 工具函数 */
int16_t EMMV5_PID_Constrain(int16_t value, int16_t min, int16_t max);
float EMMV5_PID_ConstrainFloat(float value, float min, float max);
uint32_t EMMV5_PID_GetTick(void);

/* 错误处理 */
typedef enum {
    EMMV5_PID_OK = 0,           /* 正常 */
    EMMV5_PID_ERROR,            /* 一般错误 */
    EMMV5_PID_TIMEOUT,          /* 超时错误 */
    EMMV5_PID_PARSE_ERROR,      /* 解析错误 */
    EMMV5_PID_MOTOR_ERROR,      /* 电机错误 */
    EMMV5_PID_UART_ERROR        /* 串口错误 */
} EMMV5_PID_Status_t;

/* 系统实例获取函数 (避免全局变量内存冲突) */
EMMV5_PID_System_t* EMMV5_PID_GetSystemInstance(void);

#endif /* __EMMV5_PID_H */
