# K230D坐标驱动二维步进云台追踪系统架构设计

## 1. 架构概述

### 1.1 系统架构图

```
┌─────────────────┐    UART6     ┌─────────────────────────────────────┐
│   K230D开发板    │ ──────────► │         STM32F407主控               │
│                 │              │                                     │
│ • 红色目标识别   │              │  ┌─────────────────────────────────┐ │
│ • 坐标计算      │              │  │        数据接收层               │ │
│ • 串口发送      │              │  │  • K230D_ProcessData()         │ │
└─────────────────┘              │  │  • DMA环形缓冲区               │ │
                                 │  │  • 数据包解析                  │ │
                                 │  └─────────────────────────────────┘ │
                                 │                │                     │
                                 │                ▼                     │
                                 │  ┌─────────────────────────────────┐ │
                                 │  │        控制算法层               │ │
                                 │  │  • PID_Compute()               │ │
                                 │  │  • 双轴独立控制                │ │
                                 │  │  • 死区处理                    │ │
                                 │  └─────────────────────────────────┘ │
                                 │                │                     │
                                 │                ▼                     │
                                 │  ┌─────────────────────────────────┐ │
                                 │  │        电机驱动层               │ │
                                 │  │  • EMMV5_PID_MoveMotor()       │ │
                                 │  │  • 速度限制和保护              │ │
                                 │  │  • 双UART通信                  │ │
                                 │  └─────────────────────────────────┘ │
                                 └─────────────────────────────────────┘
                                              │        │
                                         UART2│        │UART4
                                              ▼        ▼
                                 ┌─────────────────────────────────────┐
                                 │         EMMV5步进云台               │
                                 │                                     │
                                 │  X轴电机(0x01)    Y轴电机(0x02)     │
                                 │      │                │             │
                                 │      ▼                ▼             │
                                 │  ┌─────────┐    ┌─────────┐         │
                                 │  │ 水平转动 │    │ 垂直转动 │         │
                                 │  └─────────┘    └─────────┘         │
                                 └─────────────────────────────────────┘
```

### 1.2 数据流设计

#### 1.2.1 数据流向
1. **K230D → STM32**: 红色目标坐标数据 (30Hz)
2. **STM32内部**: 坐标解析 → PID计算 → 电机指令生成
3. **STM32 → EMMV5**: 双轴电机控制指令 (20Hz)

#### 1.2.2 数据格式标准
```c
// K230D发送格式
"RED_CENTER:X=320,Y=240\n"

// 内部数据结构
typedef struct {
    int16_t x, y;           // 坐标值
    uint32_t timestamp;     // 时间戳
    uint8_t data_updated;   // 更新标志
} K230D_Data_t;

// PID控制结构
typedef struct {
    float kp, ki, kd;       // PID参数
    float setpoint, input;  // 目标值和输入值
    float output;           // 控制输出
    float error, integral;  // 误差和积分项
} PID_Controller_t;
```

## 2. 模块架构设计

### 2.1 数据接收模块 (K230D_getData)

#### 2.1.1 核心功能
- **DMA环形缓冲区**: 256字节缓冲区，避免数据丢失
- **数据包解析**: 识别"RED_CENTER"协议格式
- **异常处理**: 超时检测、数据校验、缓冲区清理

#### 2.1.2 关键接口
```c
HAL_StatusTypeDef K230D_Init(UART_HandleTypeDef *uart_handle);
void K230D_ProcessData(void);                    // 主循环调用
uint8_t K230D_GetCoordinates(int16_t *x, int16_t *y);
uint8_t K230D_IsDataUpdated(void);
uint32_t K230D_GetDataAge(void);
```

#### 2.1.3 性能指标
- **接收频率**: 30Hz
- **延迟**: <10ms
- **成功率**: ≥95%

### 2.2 PID控制模块 (EMMV5_PID)

#### 2.2.1 双轴独立控制架构
```c
typedef struct {
    PID_Controller_t x_pid;     // X轴PID控制器
    PID_Controller_t y_pid;     // Y轴PID控制器
    TargetCoord_t target;       // 目标坐标
    GimbalStatus_t gimbal;      // 云台状态
} EMMV5_PID_System_t;
```

#### 2.2.2 控制算法设计
- **PID参数**: Kp=2.0, Ki=0.1, Kd=0.5
- **目标中心**: (320, 240) - 640x480分辨率中心
- **死区处理**: ±5像素死区，避免抖动
- **输出限制**: ±1000速度范围

#### 2.2.3 关键接口
```c
void EMMV5_PID_Init(EMMV5_PID_System_t *system, ...);
void EMMV5_PID_SetTarget(EMMV5_PID_System_t *system, int16_t x, int16_t y);
void EMMV5_PID_UpdateControl(EMMV5_PID_System_t *system);
float PID_Compute(PID_Controller_t *pid, float setpoint, float input);
```

### 2.3 电机驱动模块 (EMMV5)

#### 2.3.1 双电机控制架构
- **X轴电机**: 地址0x01, UART2通信
- **Y轴电机**: 地址0x02, UART4通信
- **独立控制**: 每轴独立PID输出和电机控制

#### 2.3.2 安全保护机制
- **速度限制**: 50-800 RPM范围
- **加速度控制**: 500 RPM/s²
- **超时保护**: 200ms无数据自动停止
- **故障检测**: 电机无响应检测

#### 2.3.3 关键接口
```c
void EMMV5_PID_MoveMotor(UART_HandleTypeDef *huart, uint8_t addr, 
                         int16_t speed, uint8_t acceleration);
void EMMV5_PID_EnableMotors(EMMV5_PID_System_t *system, bool enable);
void EMMV5_PID_StopMotors(EMMV5_PID_System_t *system);
```

## 3. 系统集成方案

### 3.1 主函数架构设计

#### 3.1.1 初始化序列
```c
void System_Init(void) {
    1. HAL库和硬件初始化
    2. OLED显示器初始化
    3. K230D数据接收模块初始化
    4. EMMV5 PID系统初始化
    5. 电机使能和参数配置
    6. 系统状态显示
}
```

#### 3.1.2 主循环架构
```c
void System_MainLoop(void) {
    1. K230D数据处理 (K230D_ProcessData)
    2. 坐标数据获取和验证
    3. PID控制计算 (EMMV5_PID_UpdateControl)
    4. 电机控制执行
    5. 状态监控和显示
    6. 异常处理和恢复
}
```

### 3.2 实时性保证

#### 3.2.1 时序设计
- **主循环频率**: 100Hz (10ms周期)
- **PID控制频率**: 20Hz (50ms周期)
- **状态显示频率**: 5Hz (200ms周期)

#### 3.2.2 优先级管理
1. **最高优先级**: UART中断接收
2. **高优先级**: PID控制计算
3. **中优先级**: 电机控制执行
4. **低优先级**: 状态显示和调试

### 3.3 内存和性能优化

#### 3.3.1 内存使用
- **DMA缓冲区**: 256字节
- **数据结构**: <1KB总内存占用
- **栈使用**: <512字节

#### 3.3.2 CPU优化
- **浮点运算优化**: 使用硬件FPU
- **中断处理优化**: 最小化中断处理时间
- **算法优化**: PID计算使用增量式算法

## 4. 异常处理和容错设计

### 4.1 数据异常处理
- **坐标越界**: 忽略无效坐标数据
- **数据超时**: 200ms无数据停止追踪
- **通信错误**: 自动重连和恢复

### 4.2 硬件故障处理
- **电机无响应**: 报警并停止系统
- **传感器故障**: 切换到安全模式
- **电源异常**: 紧急停机保护

### 4.3 系统恢复机制
- **自动重启**: 严重错误时系统重启
- **状态恢复**: 保存关键参数到Flash
- **故障诊断**: 详细的错误日志记录

## 5. 性能指标和验证

### 5.1 关键性能指标
- **追踪精度**: ±5像素
- **响应延迟**: ≤100ms
- **系统稳定性**: ≥2小时连续运行
- **CPU占用率**: ≤80%

### 5.2 测试验证方案
- **功能测试**: 静态和动态目标追踪
- **性能测试**: 延迟和精度测量
- **稳定性测试**: 长时间运行测试
- **边界测试**: 极限条件下的系统表现

---

**架构设计状态**: ✅ 已完成
**技术可行性**: ✅ 已验证
**下一步**: 主函数代码实现
**负责人**: Alex (工程师)
**版权归属**: 米醋电子工作室
