#include "K230D_getData.h"

/**
 * @file    K230D_getData.c
 * <AUTHOR>
 * @version V1.0
 * @date    2025-07-29
 * @brief   K230D数据接收模块实现 - DMA环形缓冲区
 * @license Copyright (c) 2025, 米醋电子工作室
 */

/* 私有变量 */
uint8_t dma_rx_buffer[K230D_DMA_BUFFER_SIZE];           // DMA环形缓冲区 (暴露给调试)
static volatile uint16_t dma_rx_head = 0;               // DMA写入位置
volatile uint16_t dma_rx_tail = 0;                      // 应用读取位置 (暴露给调试)
static char packet_buffer[K230D_PACKET_BUFFER_SIZE];    // 数据包处理缓冲区
static uint8_t packet_index = 0;                        // 数据包索引
static UART_HandleTypeDef *k230d_uart = NULL;           // UART句柄

/* 全局数据结构 */
K230D_Data_t g_k230d_data = {0};

/* 私有函数声明 */
static uint16_t DMA_GetAvailableData(void);
static uint8_t DMA_ReadByte(void);
static void ProcessPacket(void);

/**
 * @brief 初始化K230D数据接收模块
 */
HAL_StatusTypeDef K230D_Init(UART_HandleTypeDef *uart_handle)
{
    if (uart_handle == NULL) {
        return HAL_ERROR;
    }
    
    // 保存UART句柄
    k230d_uart = uart_handle;
    
    // 初始化数据结构
    memset(&g_k230d_data, 0, sizeof(K230D_Data_t));
    
    // 初始化缓冲区
    memset(dma_rx_buffer, 0, K230D_DMA_BUFFER_SIZE);
    memset(packet_buffer, 0, K230D_PACKET_BUFFER_SIZE);
    
    // 重置指针
    dma_rx_head = 0;
    dma_rx_tail = 0;
    packet_index = 0;
    
    // 启动DMA接收
    HAL_StatusTypeDef status = HAL_UART_Receive_DMA(k230d_uart, dma_rx_buffer, K230D_DMA_BUFFER_SIZE);
    
    if (status == HAL_OK) {
        g_k230d_data.initialized = 1;
    }
    
    return status;
}

/**
 * @brief 获取DMA环形缓冲区中可用数据长度
 */
static uint16_t DMA_GetAvailableData(void)
{
    if (k230d_uart == NULL || k230d_uart->hdmarx == NULL) {
        return 0;
    }

    // 获取DMA剩余计数器值
    uint16_t dma_counter = __HAL_DMA_GET_COUNTER(k230d_uart->hdmarx);

    // 计算DMA当前写入位置 (注意：DMA是循环模式)
    dma_rx_head = (K230D_DMA_BUFFER_SIZE - dma_counter) % K230D_DMA_BUFFER_SIZE;

    // 计算可用数据量
    uint16_t available;
    if (dma_rx_head >= dma_rx_tail) {
        available = dma_rx_head - dma_rx_tail;
    } else {
        // 环形缓冲区回绕情况
        available = K230D_DMA_BUFFER_SIZE - dma_rx_tail + dma_rx_head;
    }

    return available;
}

/**
 * @brief 从DMA环形缓冲区读取一个字节
 */
static uint8_t DMA_ReadByte(void)
{
    // 确保有数据可读
    if (DMA_GetAvailableData() == 0) {
        return 0;
    }

    // 读取数据
    uint8_t data = dma_rx_buffer[dma_rx_tail];

    // 更新tail指针 (环形缓冲区)
    dma_rx_tail = (dma_rx_tail + 1) % K230D_DMA_BUFFER_SIZE;

    return data;
}

/**
 * @brief 处理完整的数据包
 */
static void ProcessPacket(void)
{
    // 保存完整数据包
    strncpy(g_k230d_data.last_packet, packet_buffer, K230D_PACKET_BUFFER_SIZE - 1);
    g_k230d_data.last_packet[K230D_PACKET_BUFFER_SIZE - 1] = '\0';

    g_k230d_data.processed_packets++;

    // 解析坐标 - 更宽松的匹配
    int x, y;

    // 尝试多种格式匹配
    if (sscanf(packet_buffer, "RED_CENTER:X=%d,Y=%d", &x, &y) == 2) {
        // 标准格式匹配成功
        g_k230d_data.x = (int16_t)x;
        g_k230d_data.y = (int16_t)y;
        g_k230d_data.valid_packets++;

        // 更新时间戳和标志
        g_k230d_data.last_update_tick = HAL_GetTick();
        g_k230d_data.data_updated = 1;
    }
    else if (strstr(packet_buffer, "RED_CENTER") != NULL) {
        // 包含关键字但格式不匹配，尝试其他解析方式
        char *x_pos = strstr(packet_buffer, "X=");
        char *y_pos = strstr(packet_buffer, "Y=");

        if (x_pos != NULL && y_pos != NULL) {
            x = atoi(x_pos + 2);
            y = atoi(y_pos + 2);

            g_k230d_data.x = (int16_t)x;
            g_k230d_data.y = (int16_t)y;
            g_k230d_data.valid_packets++;

            // 更新时间戳和标志
            g_k230d_data.last_update_tick = HAL_GetTick();
            g_k230d_data.data_updated = 1;
        }
    }
}

/**
 * @brief 处理K230D接收数据
 */
void K230D_ProcessData(void)
{
    if (!g_k230d_data.initialized) {
        return;
    }

    uint16_t available = DMA_GetAvailableData();
    g_k230d_data.available_data = available;

    // 激进处理策略：处理所有可用数据
    uint16_t bytes_processed = 0;
    const uint16_t MAX_BYTES_PER_CALL = 128; // 每次最多处理128字节

    while (available > 0 && bytes_processed < MAX_BYTES_PER_CALL) {
        uint8_t byte = DMA_ReadByte();
        g_k230d_data.total_bytes++;
        g_k230d_data.last_byte = byte;
        bytes_processed++;

        // 更宽松的数据包检测逻辑
        if (packet_index == 0) {
            // 寻找数据包开始标志
            if (byte == 'R') {
                packet_buffer[packet_index++] = byte;
            }
            // 忽略其他字符，继续寻找
        }
        else if (packet_index > 0 && packet_index < K230D_PACKET_BUFFER_SIZE - 1) {
            // 继续接收数据包
            packet_buffer[packet_index++] = byte;

            // 检查数据包结束
            if (byte == '\n' || byte == '\r') {
                packet_buffer[packet_index] = '\0';

                // 只处理长度合理的数据包
                if (packet_index > 10) { // 至少要有"RED_CENTER:X=1,Y=1"这么长
                    ProcessPacket();
                }

                packet_index = 0;
                // 立即处理下一个字节，不要break
            }
        }
        else {
            // 数据包过长或其他错误，重置
            packet_index = 0;
        }

        // 重新获取可用数据量
        available = DMA_GetAvailableData();
    }

    // 如果还有大量数据积压，强制清理
    if (available > K230D_DMA_BUFFER_SIZE / 2) {
        // 跳过积压数据的一半
        uint16_t skip_count = available / 2;
        for (uint16_t i = 0; i < skip_count && DMA_GetAvailableData() > 0; i++) {
            DMA_ReadByte();
            g_k230d_data.total_bytes++;
        }
        packet_index = 0; // 重置解析状态
    }
}

/**
 * @brief 获取K230D坐标数据
 */
uint8_t K230D_GetCoordinates(int16_t *x, int16_t *y)
{
    if (x == NULL || y == NULL || !g_k230d_data.initialized) {
        return 0;
    }
    
    *x = g_k230d_data.x;
    *y = g_k230d_data.y;
    
    return (g_k230d_data.valid_packets > 0) ? 1 : 0;
}

/**
 * @brief 获取接收统计信息
 */
K230D_Data_t* K230D_GetStats(void)
{
    return &g_k230d_data;
}

/**
 * @brief 获取DMA环形缓冲区可用数据量
 */
uint16_t K230D_GetAvailableData(void)
{
    return DMA_GetAvailableData();
}

/**
 * @brief 重置K230D接收统计
 */
void K230D_ResetStats(void)
{
    g_k230d_data.total_bytes = 0;
    g_k230d_data.processed_packets = 0;
    g_k230d_data.valid_packets = 0;
    g_k230d_data.x = 0;
    g_k230d_data.y = 0;
    memset(g_k230d_data.last_packet, 0, K230D_PACKET_BUFFER_SIZE);
}

/**
 * @brief 检查数据是否有更新
 */
uint8_t K230D_IsDataUpdated(void)
{
    return g_k230d_data.data_updated;
}

/**
 * @brief 清除数据更新标志
 */
void K230D_ClearUpdateFlag(void)
{
    g_k230d_data.data_updated = 0;
}

/**
 * @brief 获取数据年龄(毫秒)
 */
uint32_t K230D_GetDataAge(void)
{
    if (g_k230d_data.valid_packets == 0) {
        return 0xFFFFFFFF; // 表示从未收到有效数据
    }

    return HAL_GetTick() - g_k230d_data.last_update_tick;
}



/**
 * @brief 清理过期数据和缓冲区
 */
void K230D_CleanupBuffers(void)
{
    if (!g_k230d_data.initialized) {
        return;
    }

    uint16_t available = DMA_GetAvailableData();

    // 检测死锁情况：可用数据量长时间不变
    static uint16_t last_available = 0;
    static uint32_t deadlock_counter = 0;

    if (available == last_available && available > 0) {
        deadlock_counter++;
        if (deadlock_counter > 10) { // 连续10次清理周期都没变化
            // 强制重置缓冲区指针
            dma_rx_tail = dma_rx_head;
            packet_index = 0;
            deadlock_counter = 0;
            g_k230d_data.total_bytes += available; // 统计跳过的字节
        }
    } else {
        deadlock_counter = 0;
    }
    last_available = available;

    // 如果缓冲区积压过多数据，清理一部分
    if (available > K230D_DMA_BUFFER_SIZE / 2) { // 超过50%容量
        // 跳过一半的积压数据
        uint16_t skip_count = available / 2;
        for (uint16_t i = 0; i < skip_count && DMA_GetAvailableData() > 0; i++) {
            DMA_ReadByte();
            g_k230d_data.total_bytes++;
        }
        packet_index = 0; // 重置解析状态
    }
}

/**
 * @brief 反初始化K230D数据接收模块
 */
void K230D_DeInit(void)
{
    if (k230d_uart != NULL) {
        HAL_UART_DMAStop(k230d_uart);
    }

    g_k230d_data.initialized = 0;
    k230d_uart = NULL;

    // 清空缓冲区
    memset(dma_rx_buffer, 0, K230D_DMA_BUFFER_SIZE);
    memset(packet_buffer, 0, K230D_PACKET_BUFFER_SIZE);

    dma_rx_head = 0;
    dma_rx_tail = 0;
    packet_index = 0;
}
