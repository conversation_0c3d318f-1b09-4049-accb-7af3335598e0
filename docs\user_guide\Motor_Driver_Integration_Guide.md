# STM32F407电机驱动集成指南

## 📋 概述

本文档说明如何使用已集成到STM32F407多传感器系统中的DRV8871电机驱动模块。

### 系统组成
- **主控**: STM32F407VETx微控制器
- **电机驱动芯片**: DRV8871 (双PWM通道控制)
- **PWM定时器**: TIM1 (4通道，支持2个电机)
- **控制精度**: 1000级PWM精度 (0-999)
- **速度范围**: -100.0f 到 +100.0f (浮点数控制)

---

## 🔌 硬件连接

### 电机驱动连接 (DRV8871)

#### 左电机 (Motor Left)
| 功能 | STM32引脚 | TIM1通道 | DRV8871引脚 | 说明 |
|------|-----------|----------|-------------|------|
| AIN1 | PE9       | TIM1_CH1 | AIN1        | PWM控制信号1 |
| AIN2 | PE11      | TIM1_CH2 | AIN2        | PWM控制信号2 |

#### 右电机 (Motor Right)
| 功能 | STM32引脚 | TIM1通道 | DRV8871引脚 | 说明 |
|------|-----------|----------|-------------|------|
| AIN1 | PE13      | TIM1_CH3 | AIN1        | PWM控制信号1 |
| AIN2 | PE14      | TIM1_CH4 | AIN2        | PWM控制信号2 |

#### 电源连接
| 功能 | 连接 | 说明 |
|------|------|------|
| VCC  | 3.3V | 逻辑电源 |
| VM   | 6-45V | 电机电源 (根据电机规格) |
| GND  | GND  | 公共地 |

---

## 💻 软件架构

### 核心特性
- **双衰减模式**: 支持快衰减和慢衰减模式
- **状态驱动控制**: 动态GPIO/PWM模式切换
- **浮点速度控制**: -100.0f到+100.0f精确控制
- **反装电机支持**: 自动处理电机安装方向
- **实时状态显示**: OLED显示电机运行状态

### 主要函数

#### 初始化函数
```c
void Motor_Init(void);  // 电机系统初始化
```

#### 控制函数
```c
// 设置电机速度 (-100.0f 到 +100.0f)
void Motor_SetSpeed(Motor_t* motor, float speed);

// 设置衰减模式
void Motor_SetDecayMode(Motor_t* motor, MotorDecayMode_t mode);

// 启用/禁用电机
void Motor_Enable(Motor_t* motor, uint8_t enable);
```

---

## 🚀 使用流程

### 1. 系统初始化
系统启动时会自动初始化电机驱动：
```c
// 在main()函数中已集成
Motor_Init();  // 初始化双电机
```

### 2. 电机控制示例
```c
// 设置左电机前进50%速度
Motor_SetSpeed(&motor_left, 50.0f);

// 设置右电机后退30%速度  
Motor_SetSpeed(&motor_right, -30.0f);

// 停止所有电机
Motor_SetSpeed(&motor_left, 0.0f);
Motor_SetSpeed(&motor_right, 0.0f);
```

### 3. 衰减模式设置
```c
// 设置快衰减模式 (更好的制动性能)
Motor_SetDecayMode(&motor_left, MOTOR_DECAY_FAST);

// 设置慢衰减模式 (更平滑的运行)
Motor_SetDecayMode(&motor_right, MOTOR_DECAY_SLOW);
```

---

## 📊 OLED显示说明

### 显示布局 (128x32 OLED)
```
行1: JY+G+M  RX:123 OK    // 系统状态
行2: R:+12 P:-34 Y:+56    // JY901S角度数据
行3: A:123 G:456 M:789    // JY901S传感器数据
行4: [灰度数据] M:+30     // 灰度传感器+电机状态
```

### 电机状态显示
- **M:0** - 电机停止
- **M:+30** - 电机正转30%速度
- **M:-25** - 电机反转25%速度

---

## 🔧 测试功能

### 自动测试序列
系统包含自动电机测试功能，测试序列如下：
1. **0秒**: 停止 (0%)
2. **1秒**: 前进 (30%)
3. **2秒**: 停止 (0%)
4. **3秒**: 后退 (-30%)
5. **4秒**: 停止 (0%)
6. **循环重复**

### 手动控制
可以通过修改`Motor_Test()`函数实现自定义控制逻辑。

---

## ⚙️ 技术参数

### PWM配置
- **定时器**: TIM1
- **频率**: 12kHz (168MHz/7/2000)
- **分辨率**: 1000级 (0-999)
- **占空比范围**: 0-100%

### 电机参数
- **速度范围**: -100.0f 到 +100.0f
- **最小PWM (快衰减)**: 50/999 (5%)
- **最小PWM (慢衰减)**: 599/999 (60%)
- **制动模式**: 动态制动

### 性能指标
- **响应时间**: <1ms
- **控制精度**: 0.1%
- **更新频率**: 10Hz (与主循环同步)

---

## 🛠️ 故障排除

### 常见问题

#### 1. 电机不转动
**可能原因**:
- 电机电源未连接
- PWM信号线接触不良
- 电机驱动芯片损坏

**解决方案**:
- 检查VM电源连接
- 用示波器检查PE9/PE11/PE13/PE14的PWM信号
- 更换DRV8871芯片

#### 2. 电机转向错误
**可能原因**:
- 电机接线错误
- 软件中电机反装参数设置错误

**解决方案**:
- 交换电机的两根线
- 修改`Motor_Create()`中的reverse参数

#### 3. 电机运行不平滑
**可能原因**:
- 衰减模式不合适
- PWM频率过低
- 电机电源纹波过大

**解决方案**:
- 尝试不同的衰减模式
- 增加电源滤波电容
- 检查电机规格匹配

---

## 📝 开发注意事项

1. **电源设计**: 确保VM电源能提供足够的电流
2. **散热设计**: DRV8871在大电流时需要散热
3. **EMI防护**: 添加适当的滤波电路
4. **安全保护**: 建议添加过流保护电路
5. **调试工具**: 使用示波器监控PWM信号质量

---

## 📚 相关文档

- [STM32F407多传感器系统使用指南](Multi_Sensor_Usage_Guide.md)
- [JY901S传感器集成指南](../development/JY901S_Integration_Guide.md)
- [灰度传感器集成文档](../development/Grayscale_Sensor_Integration.md)
- [DRV8871数据手册](https://www.ti.com/lit/ds/symlink/drv8871.pdf)

---

*文档版本: v1.0*  
*最后更新: 2025-07-28*
