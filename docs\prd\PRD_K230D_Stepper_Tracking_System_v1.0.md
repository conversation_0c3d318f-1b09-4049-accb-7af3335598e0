# K230D坐标驱动二维步进云台追踪系统产品需求文档

## 1. 文档信息

| 项目 | 内容 |
|------|------|
| **文档版本** | V1.0 |
| **创建日期** | 2025-07-29 |
| **负责人** | Emma (产品经理) |
| **项目名称** | K230D坐标驱动二维步进云台追踪系统 |
| **版权归属** | 米醋电子工作室 |

## 2. 背景与问题陈述

### 2.1 项目背景
当前项目已具备K230D视觉识别模块和STM32F407控制系统的基础架构，需要在主函数中实现完整的红色目标追踪功能，将K230D识别的坐标数据转换为精确的步进云台控制指令。

### 2.2 核心问题
- **数据流断点**: K230D坐标数据与云台控制之间缺少完整的处理链路
- **控制精度需求**: 需要实现高精度的PID控制算法确保追踪稳定性
- **实时性要求**: 追踪系统需要满足实时响应要求，延迟控制在100ms以内
- **系统集成**: 需要将现有的独立模块整合为完整的追踪系统

## 3. 目标与成功指标

### 3.1 项目目标 (Objectives)
1. **O1**: 实现K230D坐标数据的实时接收和处理
2. **O2**: 构建高精度PID控制算法实现云台精确追踪
3. **O3**: 集成EMMV5步进电机驱动实现二维云台控制
4. **O4**: 建立完整的系统监控和状态显示功能

### 3.2 关键结果 (Key Results)
1. **KR1**: 坐标数据接收成功率 ≥ 95%
2. **KR2**: 追踪响应延迟 ≤ 100ms
3. **KR3**: 追踪精度误差 ≤ ±5像素
4. **KR4**: 系统连续运行稳定性 ≥ 2小时

### 3.3 反向指标 (Counter Metrics)
1. **CM1**: 系统CPU占用率 ≤ 80%
2. **CM2**: 内存使用率 ≤ 70%
3. **CM3**: 电机发热温度 ≤ 60°C

## 4. 用户画像与用户故事

### 4.1 目标用户
- **主要用户**: 嵌入式开发工程师、机器视觉应用开发者
- **使用场景**: 自动追踪系统、安防监控、教学演示

### 4.2 用户故事
1. **US1**: 作为开发者，我希望系统能自动追踪红色目标，无需手动干预
2. **US2**: 作为用户，我希望看到实时的追踪状态和坐标信息
3. **US3**: 作为维护人员，我希望系统具备故障自诊断和恢复能力

## 5. 功能规格详述

### 5.1 核心功能模块

#### 5.1.1 K230D数据接收模块
- **功能描述**: 通过USART6接收K230D发送的红色目标坐标
- **数据格式**: `RED_CENTER:X=320,Y=240\n`
- **接收频率**: 30Hz
- **缓冲机制**: DMA环形缓冲区，大小256字节

#### 5.1.2 PID控制算法模块
- **X轴PID参数**: Kp=2.0, Ki=0.1, Kd=0.5
- **Y轴PID参数**: Kp=2.0, Ki=0.1, Kd=0.5
- **目标中心**: X=320, Y=240 (640x480分辨率中心)
- **死区设置**: ±10像素

#### 5.1.3 EMMV5步进电机控制模块
- **X轴电机**: 地址0x01，连接UART2
- **Y轴电机**: 地址0x02，连接UART4
- **速度范围**: -1000 ~ +1000
- **加速度**: 500

#### 5.1.4 系统监控模块
- **OLED显示**: 实时坐标、追踪状态、系统信息
- **状态指示**: LED指示灯显示工作状态
- **性能监控**: CPU使用率、内存使用率

### 5.2 业务逻辑规则

#### 5.2.1 追踪逻辑
1. 接收K230D坐标数据
2. 计算与目标中心的偏差
3. PID算法计算控制输出
4. 转换为电机控制指令
5. 发送给EMMV5电机执行

#### 5.2.2 异常处理
1. **数据超时**: 超过200ms无数据时停止追踪
2. **坐标越界**: 坐标超出有效范围时忽略
3. **电机故障**: 电机无响应时报警并停止

## 6. 范围定义

### 6.1 包含功能 (In Scope)
- ✅ K230D坐标数据实时接收
- ✅ 双轴PID控制算法
- ✅ EMMV5步进电机驱动
- ✅ OLED状态显示
- ✅ 系统初始化和配置
- ✅ 异常处理和恢复

### 6.2 排除功能 (Out of Scope)
- ❌ 多目标追踪功能
- ❌ 颜色自适应识别
- ❌ 网络通信功能
- ❌ 数据记录和回放
- ❌ 手动控制界面

## 7. 依赖与风险

### 7.1 内部依赖项
- K230D_getData模块正常工作
- EMMV5_PID模块功能完整
- OLED显示模块可用
- UART通信稳定

### 7.2 外部依赖项
- K230D开发板正常运行红色识别程序
- EMMV5步进电机硬件连接正确
- 电源供应稳定

### 7.3 潜在风险
1. **高风险**: K230D数据传输不稳定
2. **中风险**: PID参数调优困难
3. **低风险**: 电机响应延迟

## 8. 发布初步计划

### 8.1 开发阶段
1. **阶段1**: 系统架构设计 (30分钟)
2. **阶段2**: 主函数代码实现 (60分钟)
3. **阶段3**: 集成测试和调优 (30分钟)
4. **阶段4**: 文档完善 (20分钟)

### 8.2 验收标准
- 所有功能模块正常工作
- 追踪精度满足指标要求
- 系统稳定性测试通过
- 技术文档完整

---

**文档状态**: ✅ 已完成
**下一步**: 系统架构设计
**负责人**: Bob (架构师)
