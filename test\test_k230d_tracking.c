/**
 * @file test_k230d_tracking.c
 * @brief K230D坐标驱动二维步进云台追踪系统测试程序
 * <AUTHOR> (工程师)
 * @date 2025-07-29
 * @version V1.0
 * @copyright 米醋电子工作室
 */

#include <stdio.h>
#include <stdint.h>
#include <stdbool.h>
#include <string.h>

/* 模拟HAL库函数 */
uint32_t HAL_GetTick(void) {
    static uint32_t tick = 0;
    return tick += 10;  // 模拟10ms递增
}

/* 模拟K230D数据结构 */
typedef struct {
    int16_t x, y;
    bool valid;
    uint32_t timestamp;
} MockK230D_Data_t;

/* 模拟EMMV5 PID系统结构 */
typedef struct {
    struct {
        int16_t x, y;
        bool valid;
    } target;
    bool auto_tracking;
    bool debug_mode;
    struct {
        bool tracking_active;
    } gimbal;
} MockEMMV5_PID_System_t;

/* 全局变量 */
MockK230D_Data_t mock_k230d_data = {320, 240, false, 0};
MockEMMV5_PID_System_t mock_emmv5_system = {0};
bool tracking_enabled = false;
bool debug_mode = true;
uint32_t valid_coordinates_count = 0;
uint32_t lost_target_count = 0;
uint32_t last_control_time = 0;
uint32_t control_period = 50;

/* 模拟K230D函数 */
bool K230D_GetCoordinates(int16_t *x, int16_t *y) {
    if (mock_k230d_data.valid) {
        *x = mock_k230d_data.x;
        *y = mock_k230d_data.y;
        return true;
    }
    return false;
}

uint32_t K230D_GetDataAge(void) {
    return HAL_GetTick() - mock_k230d_data.timestamp;
}

void K230D_ClearUpdateFlag(void) {
    mock_k230d_data.valid = false;
}

/* 模拟EMMV5 PID函数 */
void EMMV5_PID_SetTarget(MockEMMV5_PID_System_t *system, int16_t x, int16_t y) {
    system->target.x = x;
    system->target.y = y;
    system->target.valid = true;
    printf("EMMV5_PID_SetTarget: X=%d, Y=%d\n", x, y);
}

void EMMV5_PID_UpdateControl(MockEMMV5_PID_System_t *system) {
    if (system->auto_tracking && system->target.valid) {
        printf("EMMV5_PID_UpdateControl: 控制更新 X=%d, Y=%d\n", 
               system->target.x, system->target.y);
    }
}

/* 追踪控制函数 */
void K230D_Tracking_EnableTracking(bool enable) {
    tracking_enabled = enable;
    
    if (enable) {
        mock_emmv5_system.auto_tracking = true;
        mock_emmv5_system.gimbal.tracking_active = true;
        printf("追踪已启用\n");
    } else {
        mock_emmv5_system.auto_tracking = false;
        mock_emmv5_system.gimbal.tracking_active = false;
        printf("追踪已禁用\n");
    }
}

/* 核心追踪处理函数 (从main.c复制) */
void K230D_Tracking_Process(void) {
    uint32_t current_time = HAL_GetTick();
    int16_t x, y;
    
    /* 控制频率限制 (20Hz) */
    if (current_time - last_control_time < control_period) {
        return;
    }
    last_control_time = current_time;
    
    /* 获取K230D坐标数据 */
    if (K230D_GetCoordinates(&x, &y)) {
        /* 检查数据有效性 */
        if (x >= 0 && x <= 640 && y >= 0 && y <= 480) {
            /* 设置追踪目标 */
            EMMV5_PID_SetTarget(&mock_emmv5_system, x, y);
            
            /* 启用追踪 */
            if (!tracking_enabled) {
                K230D_Tracking_EnableTracking(true);
            }
            
            /* 更新统计 */
            valid_coordinates_count++;
            
            if (debug_mode) {
                printf("Target: X=%d, Y=%d\n", x, y);
            }
        } else {
            /* 坐标越界，忽略此次数据 */
            if (debug_mode) {
                printf("Invalid coordinates: X=%d, Y=%d\n", x, y);
            }
        }
        
        /* 清除数据更新标志 */
        K230D_ClearUpdateFlag();
    } else {
        /* 检查数据超时 */
        uint32_t data_age = K230D_GetDataAge();
        if (data_age > 200 && tracking_enabled) {
            /* 数据超时，停止追踪 */
            K230D_Tracking_EnableTracking(false);
            lost_target_count++;
            
            if (debug_mode) {
                printf("Target lost, data age: %lums\n", data_age);
            }
        }
    }
    
    /* 执行PID控制更新 */
    if (tracking_enabled) {
        EMMV5_PID_UpdateControl(&mock_emmv5_system);
    }
}

/* 测试用例 */
void test_normal_tracking(void) {
    printf("\n=== 测试1: 正常追踪 ===\n");
    
    /* 模拟接收到有效坐标 */
    mock_k230d_data.x = 320;
    mock_k230d_data.y = 240;
    mock_k230d_data.valid = true;
    mock_k230d_data.timestamp = HAL_GetTick();
    
    /* 执行追踪处理 */
    K230D_Tracking_Process();
    
    printf("有效坐标计数: %lu\n", valid_coordinates_count);
    printf("追踪状态: %s\n", tracking_enabled ? "启用" : "禁用");
}

void test_invalid_coordinates(void) {
    printf("\n=== 测试2: 无效坐标 ===\n");
    
    /* 模拟接收到无效坐标 */
    mock_k230d_data.x = 800;  // 超出640范围
    mock_k230d_data.y = 600;  // 超出480范围
    mock_k230d_data.valid = true;
    mock_k230d_data.timestamp = HAL_GetTick();
    
    uint32_t old_count = valid_coordinates_count;
    
    /* 执行追踪处理 */
    K230D_Tracking_Process();
    
    printf("有效坐标计数变化: %lu -> %lu\n", old_count, valid_coordinates_count);
}

void test_data_timeout(void) {
    printf("\n=== 测试3: 数据超时 ===\n");
    
    /* 先启用追踪 */
    K230D_Tracking_EnableTracking(true);
    
    /* 模拟数据超时 */
    mock_k230d_data.valid = false;
    mock_k230d_data.timestamp = HAL_GetTick() - 300;  // 300ms前的数据
    
    uint32_t old_lost_count = lost_target_count;
    
    /* 执行追踪处理 */
    K230D_Tracking_Process();
    
    printf("丢失目标计数变化: %lu -> %lu\n", old_lost_count, lost_target_count);
    printf("追踪状态: %s\n", tracking_enabled ? "启用" : "禁用");
}

void test_control_frequency(void) {
    printf("\n=== 测试4: 控制频率限制 ===\n");
    
    /* 重置控制时间 */
    last_control_time = HAL_GetTick();
    
    /* 模拟有效数据 */
    mock_k230d_data.x = 400;
    mock_k230d_data.y = 300;
    mock_k230d_data.valid = true;
    mock_k230d_data.timestamp = HAL_GetTick();
    
    printf("第1次调用 (应该被频率限制):\n");
    K230D_Tracking_Process();
    
    /* 等待足够时间 */
    for (int i = 0; i < 6; i++) {
        HAL_GetTick();  // 模拟时间推进
    }
    
    printf("第2次调用 (应该正常执行):\n");
    K230D_Tracking_Process();
}

/* 主测试函数 */
int main(void) {
    printf("=== K230D坐标驱动二维步进云台追踪系统测试 ===\n");
    printf("版本: V1.0\n");
    printf("作者: Alex (工程师)\n");
    printf("日期: 2025-07-29\n");
    printf("===============================================\n");
    
    /* 初始化 */
    mock_emmv5_system.debug_mode = true;
    
    /* 执行测试用例 */
    test_normal_tracking();
    test_invalid_coordinates();
    test_data_timeout();
    test_control_frequency();
    
    /* 输出最终统计 */
    printf("\n=== 测试总结 ===\n");
    printf("有效坐标总数: %lu\n", valid_coordinates_count);
    printf("丢失目标总数: %lu\n", lost_target_count);
    printf("当前追踪状态: %s\n", tracking_enabled ? "启用" : "禁用");
    printf("测试完成!\n");
    
    return 0;
}
