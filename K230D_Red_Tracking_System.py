#####################################################################################################
# @file         K230D_Red_Tracking_System.py
# <AUTHOR>
# @version      V1.0
# @date         2025-07-28
# @brief        K230D红色追踪系统 - 检测红色目标并通过串口发送坐标
# @license      Copyright (c) 2025, 米醋电子工作室
#####################################################################################################
# @attention
#
# 实验平台: 正点原子 K230D BOX开发板
# 功能描述: 实时检测红色目标，计算中心坐标，通过UART1发送坐标数据
# 硬件连接: UART1_TX(Pin40), UART1_RX(Pin41) → STM32 USART6
# 数据格式: "RED_CENTER:X=123,Y=456\n"
#
#####################################################################################################

import time, os, sys
from media.sensor import *  # 导入sensor模块，使用摄像头相关接口
from media.display import * # 导入display模块，使用display相关接口
from media.media import *   # 导入media模块，使用media相关接口
from machine import Pin, UART, FPIOA

# 实例化FPIOA
fpioa = FPIOA()

# 配置UART1引脚功能
fpioa.set_function(40, FPIOA.UART1_TXD)  # Pin40配置为UART1发送
fpioa.set_function(41, FPIOA.UART1_RXD)  # Pin41配置为UART1接收

# 配置状态指示LED
fpioa.set_function(59, FPIOA.GPIO59)     # 蓝色LED
fpioa.set_function(61, FPIOA.GPIO61)     # 红色LED

# 初始化GPIO对象
led_blue = Pin(59, Pin.OUT, pull=Pin.PULL_NONE, drive=7)
led_red = Pin(61, Pin.OUT, pull=Pin.PULL_NONE, drive=7)

# 初始化UART1对象
uart1 = UART(UART.UART1, baudrate=115200, bits=UART.EIGHTBITS, 
             parity=UART.PARITY_NONE, stop=UART.STOPBITS_ONE)

# 红色检测阈值 (LAB色彩空间)
# 针对红色目标优化的阈值参数
RED_THRESHOLD = [(0, 80, 40, 80, 10, 80)]  # (L_min, L_max, A_min, A_max, B_min, B_max)

# 系统配置参数
CAMERA_WIDTH = 640      # 摄像头宽度
CAMERA_HEIGHT = 480     # 摄像头高度
MIN_BLOB_AREA = 100     # 最小检测区域面积
SEND_INTERVAL = 50      # 串口发送间隔(ms)

class RedTrackingSystem:
    """K230D红色追踪系统类"""
    
    def __init__(self):
        """初始化红色追踪系统"""
        self.last_send_time = 0
        self.detection_count = 0
        self.led_state = False
        
        print("K230D Red Tracking System Initializing...")

        # 初始化摄像头
        self.sensor = Sensor(width=1280, height=960)
        self.sensor.reset()
        self.sensor.set_framesize(Sensor.VGA)      # 设置为VGA分辨率(640x480)
        self.sensor.set_pixformat(Sensor.RGB565)   # 设置RGB565格式用于颜色检测

        # 初始化LCD显示
        Display.init(Display.ST7701, width=CAMERA_WIDTH, height=CAMERA_HEIGHT,
                    fps=90, to_ide=True)
        MediaManager.init()

        # 启动摄像头
        self.sensor.run()

        # 初始化时LED指示
        led_blue.value(1)  # 蓝色LED亮起表示系统就绪
        led_red.value(1)   # 红色LED亮起表示系统就绪
        time.sleep_ms(500)
        led_blue.value(0)
        led_red.value(0)

        print("Red Tracking System Ready!")
        print("UART1 Config: 115200-8N1 → STM32 USART6")
        print("Data Format: RED_CENTER:X=123,Y=456")
        
    def detect_red_objects(self, img):
        """
        检测图像中的红色目标
        
        Args:
            img: 输入图像
            
        Returns:
            list: 检测到的红色区域列表
        """
        # 使用find_blobs检测红色区域
        red_blobs = img.find_blobs(RED_THRESHOLD, 
                                  pixels_threshold=MIN_BLOB_AREA,
                                  area_threshold=MIN_BLOB_AREA,
                                  merge=True)
        return red_blobs
    
    def calculate_center(self, blob):
        """
        计算检测区域的中心坐标
        
        Args:
            blob: 检测到的区域对象
            
        Returns:
            tuple: (center_x, center_y)
        """
        center_x = blob.cx()  # 获取质心X坐标
        center_y = blob.cy()  # 获取质心Y坐标
        return (center_x, center_y)
    
    def send_coordinates(self, x, y):
        """
        通过UART1发送坐标数据
        
        Args:
            x: X坐标
            y: Y坐标
        """
        current_time = time.ticks_ms()
        
        # 控制发送频率，避免数据过于频繁
        if current_time - self.last_send_time >= SEND_INTERVAL:
            # 构造数据包格式: "RED_CENTER:X=123,Y=456\n"
            data_packet = f"RED_CENTER:X={x},Y={y}\n"
            
            # 通过UART1发送数据
            uart1.write(data_packet)
            
            # 更新发送时间
            self.last_send_time = current_time
            
            # 串口发送指示
            led_blue.value(1)
            time.sleep_ms(10)
            led_blue.value(0)
            
            print(f"Sent: {data_packet.strip()}")
    
    def draw_detection_results(self, img, red_blobs):
        """
        在图像上绘制检测结果
        
        Args:
            img: 输入图像
            red_blobs: 检测到的红色区域列表
        """
        if red_blobs:
            # 找到最大的红色区域
            largest_blob = max(red_blobs, key=lambda b: b.pixels())
            
            # 获取中心坐标
            center_x, center_y = self.calculate_center(largest_blob)
            
            # 绘制检测框
            img.draw_rectangle(largest_blob.rect(), color=(0, 255, 0), thickness=3)
            
            # 绘制中心点
            img.draw_cross(center_x, center_y, color=(255, 255, 0), size=10, thickness=2)
            
            # 绘制坐标信息
            coord_text = f"X:{center_x} Y:{center_y}"
            img.draw_string_advanced(10, 10, 24, coord_text, 
                                   color=(255, 255, 255), thickness=2)
            
            # 绘制区域面积信息
            area_text = f"Area:{largest_blob.pixels()}"
            img.draw_string_advanced(10, 40, 20, area_text, 
                                   color=(255, 255, 255), thickness=2)
            
            # 发送坐标数据
            self.send_coordinates(center_x, center_y)
            
            # 检测成功指示
            self.detection_count += 1
            if self.detection_count % 10 == 0:  # 每10次检测闪烁一次
                led_red.value(1)
                time.sleep_ms(50)
                led_red.value(0)
                
            return True
        else:
            # 未检测到红色目标
            img.draw_string_advanced(10, 10, 24, "No Red Object", 
                                   color=(255, 0, 0), thickness=2)
            return False
    
    def run(self):
        """运行红色追踪系统主循环"""
        print("Starting Red Tracking...")
        clock = time.clock()
        
        try:
            while True:
                os.exitpoint()  # 检测IDE中断
                clock.tick()    # 开始计时
                
                # 获取摄像头图像
                img = self.sensor.snapshot()
                
                # 检测红色目标
                red_blobs = self.detect_red_objects(img)
                
                # 绘制检测结果
                detection_success = self.draw_detection_results(img, red_blobs)
                
                # 绘制系统状态信息
                fps_text = f"FPS:{clock.fps():.1f}"
                img.draw_string_advanced(CAMERA_WIDTH-120, 10, 20, fps_text, 
                                       color=(0, 255, 255), thickness=2)
                
                status_text = "TRACKING" if detection_success else "SEARCHING"
                img.draw_string_advanced(CAMERA_WIDTH-120, 35, 16, status_text, 
                                       color=(0, 255, 0) if detection_success else (255, 255, 0), 
                                       thickness=2)
                
                # 显示图像
                Display.show_image(img)
                
                # 打印FPS信息
                if self.detection_count % 30 == 0:  # 每30帧打印一次
                    print(f"System Status - FPS: {clock.fps():.1f}, Detections: {self.detection_count}")
                
        except KeyboardInterrupt as e:
            print("User stop:", e)
        except BaseException as e:
            print(f"Exception: {e}")
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理系统资源"""
        print("Cleaning up resources...")
        
        # 关闭LED
        led_blue.value(0)
        led_red.value(0)
        
        # 停止摄像头
        if hasattr(self, 'sensor') and isinstance(self.sensor, Sensor):
            self.sensor.stop()
        
        # 释放显示资源
        Display.deinit()
        os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
        time.sleep_ms(100)
        
        # 释放媒体资源
        MediaManager.deinit()
        
        print("Red Tracking System Stopped.")

# 主程序入口
if __name__ == "__main__":
    # 启用IDE退出点检测
    os.exitpoint(os.EXITPOINT_ENABLE)
    
    # 创建并运行红色追踪系统
    red_tracker = RedTrackingSystem()
    red_tracker.run()
