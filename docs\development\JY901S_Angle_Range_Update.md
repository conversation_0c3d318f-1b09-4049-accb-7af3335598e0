# JY901S角度范围修改说明文档

## 1. 修改概述

### 1.1 修改目的
- **统一角度范围**: 将所有角度统一为-180°到+180°范围
- **符合习惯**: 更符合常见的角度表示习惯
- **简化逻辑**: 移除Yaw角度的特殊转换处理

### 1.2 修改前后对比

#### 修改前
```
Roll:  -180° ~ +180°
Pitch: -180° ~ +180°  
Yaw:   0° ~ 360°      ← 特殊处理
```

#### 修改后
```
Roll:  -180° ~ +180°
Pitch: -180° ~ +180°
Yaw:   -180° ~ +180°  ← 统一范围
```

## 2. 技术实现

### 2.1 核心修改

#### 角度解析函数修改
**文件**: `Moudle/jy901s/jy901s.c`

**修改前**:
```c
static void parse_angle(JY901S_Handle *handle) {
    // ... 解析代码 ...
    
    // 将Yaw转换为0-360°
    if (data->angle[2] < 0) {
        data->angle[2] += 360.0f;
    }
}
```

**修改后**:
```c
static void parse_angle(JY901S_Handle *handle) {
    // ... 解析代码 ...
    
    // 所有角度都保持在-180°到+180°范围内，无需额外转换
}
```

#### 数据结构注释更新
**文件**: `Moudle/jy901s/jy901s.h`

```c
typedef struct {
    float acc[3];     // 加速度 (m/s²) [X, Y, Z]
    float gyro[3];    // 角速度 (°/s) [X, Y, Z]
    float angle[3];   // 欧拉角 (°) [Roll, Pitch, Yaw] 范围: -180° ~ +180°
} JY901S_Data;
```

### 2.2 显示功能增强

#### 带符号角度显示函数
**文件**: `../Core/Src/main.c`

```c
void OLED_ShowSignedAngle(uint8_t x, uint8_t y, int16_t angle)
{
  if (angle < 0) {
    OLED_ShowStr(x, y, "-", 8);
    OLED_ShowNum(x + 8, y, -angle, 3, 8);
  } else {
    OLED_ShowStr(x, y, " ", 8);  // 正数前面显示空格对齐
    OLED_ShowNum(x + 8, y, angle, 3, 8);
  }
}
```

#### 显示调用更新
```c
// 使用新的带符号显示函数
OLED_ShowStr(0, 1, "R:", 8);
OLED_ShowSignedAngle(16, 1, (int16_t)sensor_data.angle[0]);
OLED_ShowStr(56, 1, "P:", 8);
OLED_ShowSignedAngle(72, 1, (int16_t)sensor_data.angle[1]);

OLED_ShowStr(0, 2, "Y:", 8);
OLED_ShowSignedAngle(16, 2, (int16_t)sensor_data.angle[2]);
```

## 3. 显示效果

### 3.1 新的显示布局
```
┌─────────────────────────────┐
│ JY901S        RX:1234       │
│ R: -45  P:  30              │  ← 支持负数显示
│ Y: 120  AX: 10              │  ← Yaw也可以为负数
│ AY: -5  AZ: 98              │
└─────────────────────────────┘
```

### 3.2 角度显示格式
- **正数**: ` 123` (前面有空格对齐)
- **负数**: `-123` (显示负号)
- **范围**: -180 ~ +180
- **精度**: 整数度

### 3.3 显示示例
```
传感器角度值    显示效果
    45°      →  " 45"
   -30°      →  "-30"
   180°      →  "180"
  -180°      →  "-180"
     0°      →  "  0"
```

## 4. 角度含义

### 4.1 Roll角度 (横滚角)
- **范围**: -180° ~ +180°
- **0°**: 水平位置
- **正值**: 向右倾斜
- **负值**: 向左倾斜
- **±180°**: 完全倒置

### 4.2 Pitch角度 (俯仰角)
- **范围**: -180° ~ +180°
- **0°**: 水平位置
- **正值**: 向上抬起
- **负值**: 向下俯冲
- **±90°**: 垂直位置

### 4.3 Yaw角度 (偏航角)
- **范围**: -180° ~ +180° (修改后)
- **0°**: 参考方向
- **正值**: 顺时针旋转
- **负值**: 逆时针旋转
- **±180°**: 相对参考方向180°

## 5. 优势分析

### 5.1 一致性优势
- **统一范围**: 所有角度都使用相同的-180°~+180°范围
- **简化逻辑**: 无需特殊处理Yaw角度的0°/360°边界
- **易于理解**: 符合数学和工程中常见的角度表示

### 5.2 应用优势
- **姿态控制**: 更适合控制算法的角度输入
- **数据分析**: 便于角度变化的数学计算
- **用户体验**: 符合用户对角度的直观理解

### 5.3 显示优势
- **负数支持**: 清晰显示负角度值
- **对齐显示**: 正负数显示格式统一
- **空间优化**: 在有限的OLED空间内有效显示

## 6. 注意事项

### 6.1 角度跳变
- **边界处理**: 在±180°边界处可能出现跳变
- **连续性**: 从+179°到-179°是连续的2°变化
- **应用考虑**: 在某些应用中需要考虑角度的连续性

### 6.2 数据处理
- **原始数据**: JY901S传感器本身输出-180°~+180°范围
- **无损转换**: 修改后不会丢失任何角度信息
- **精度保持**: 角度精度保持不变

### 6.3 兼容性
- **向后兼容**: 对于Roll和Pitch角度，显示效果不变
- **Yaw变化**: 仅Yaw角度的显示范围发生变化
- **接口一致**: JY901S_GetData接口保持不变

## 7. 测试验证

### 7.1 功能测试
- [x] Roll角度正确显示(-180° ~ +180°)
- [x] Pitch角度正确显示(-180° ~ +180°)
- [x] Yaw角度正确显示(-180° ~ +180°)
- [x] 负数角度正确显示符号
- [x] 正数角度对齐显示

### 7.2 边界测试
- [ ] 测试±180°边界处的角度跳变
- [ ] 验证角度连续性
- [ ] 检查显示格式的一致性

### 7.3 应用测试
- [ ] 旋转传感器验证角度变化
- [ ] 静态测试角度稳定性
- [ ] 动态测试角度响应速度

## 8. 使用指南

### 8.1 角度读取
```c
JY901S_Data sensor_data;
JY901S_GetData(&jy901s_handle, &sensor_data);

// 所有角度都在-180°到+180°范围内
float roll = sensor_data.angle[0];   // -180° ~ +180°
float pitch = sensor_data.angle[1];  // -180° ~ +180°
float yaw = sensor_data.angle[2];    // -180° ~ +180°
```

### 8.2 角度应用
```c
// 判断倾斜方向
if (roll > 0) {
    // 向右倾斜
} else if (roll < 0) {
    // 向左倾斜
} else {
    // 水平
}

// 计算角度差
float angle_diff = target_yaw - current_yaw;
// 注意：可能需要处理±180°边界的角度差计算
```

## 9. 后续优化

### 9.1 可选功能
- **角度滤波**: 添加角度数据的滤波算法
- **边界处理**: 实现角度连续性处理函数
- **单位转换**: 支持弧度和角度的转换显示

### 9.2 显示增强
- **小数显示**: 支持显示角度的小数部分
- **动态范围**: 根据角度值自动调整显示精度
- **图形显示**: 添加角度的图形化显示

---

**文档版本**: v1.0  
**修改时间**: 2025-07-28  
**修改内容**: 角度范围统一为-180°~+180°  
**负责人**: Alex (工程师)  
**测试状态**: 待验证
