#####################################################################################################
# @file         main.py
# <AUTHOR>
# @version      V1.0
# @date         2024-09-12
# @brief        RTC实验
# @license      Copyright (c) 2020-2032, 广州市星翼电子科技有限公司
#####################################################################################################
# @attention
#
# 实验平台:正点原子 K230D BOX开发板
# 在线视频:www.yuanzige.com
# 技术论坛:www.openedv.com
# 公司网址:www.alientek.com
# 购买地址:openedv.taobao.com
#
#####################################################################################################

from machine import RTC
import time

# 构建RTC对象
rtc = RTC()

# 设置RTC日期和时间。(2024, 9, 12, 3, 8, 0, 0, 0)按顺序分别表示（年，月，日，星期，时，分，秒，微妙），
# 其中星期使用0-6表示星期一到星期日。

rtc.datetime((2024, 9, 12, 3, 8, 0, 0, 0))

while True:
    print(rtc.datetime()) #打印时间
    time.sleep(1) #延时1秒
