# STM32F407多传感器系统使用指南

## 📋 概述

本文档详细说明如何使用STM32F407项目中集成的JY901S九轴传感器和8路灰度传感器系统。

### 系统组成
- **主控**: STM32F407VETx微控制器
- **姿态传感器**: JY901S九轴传感器 (加速度计+陀螺仪+磁力计)
- **灰度传感器**: 感为智能8路灰度传感器
- **显示器**: 0.91寸OLED显示屏 (128x32分辨率)
- **通信**: UART5 + 软件I2C

---

## 🔌 硬件连接

### JY901S传感器连接
| 功能 | STM32引脚 | JY901S引脚 | 说明 |
|------|-----------|------------|------|
| TX   | PC12      | RX         | UART5发送 |
| RX   | PD2       | TX         | UART5接收 |
| VCC  | 5V        | VCC        | 电源正极 |
| GND  | GND       | GND        | 电源负极 |

### 灰度传感器连接
| 功能 | STM32引脚 | 传感器引脚 | 说明 |
|------|-----------|------------|------|
| SCL  | PC4       | SCL        | I2C时钟线 |
| SDA  | PC5       | SDA        | I2C数据线 |
| VCC  | 3.3V      | VCC        | 电源正极 |
| GND  | GND       | GND        | 电源负极 |

### OLED显示屏连接
| 功能 | STM32引脚 | OLED引脚 | 说明 |
|------|-----------|----------|------|
| SCL  | PB10      | SCL      | I2C2时钟线 |
| SDA  | PB11      | SDA      | I2C2数据线 |
| VCC  | 3.3V      | VCC      | 电源正极 |
| GND  | GND       | GND      | 电源负极 |

---

## 💻 代码使用流程

### 1. 系统初始化

```c
int main(void)
{
  /* 系统初始化 */
  HAL_Init();
  SystemClock_Config();
  
  /* 外设初始化 */
  MX_GPIO_Init();
  MX_I2C2_Init();
  MX_UART5_Init();
  
  /* 传感器初始化 */
  OLED_Init();                    // OLED显示器初始化
  JY901S_Init(&jy901s_handle, &huart5, NULL);  // JY901S初始化
  HAL_UART_Receive_IT(&huart5, uart_rx_buffer, 1);  // 启动UART中断接收
  GrayScale_Init();               // 灰度传感器初始化
  
  /* 显示启动信息 */
  OLED_Clear();
  OLED_ShowStr(16, 1, "JY901S+Gray", 8);
  OLED_ShowStr(32, 2, "Ready!", 8);
  HAL_Delay(2000);
  
  /* 主循环 */
  while (1)
  {
    // 传感器数据处理和显示
    SensorDataProcess();
    HAL_Delay(100);  // 10Hz刷新率
  }
}
```

### 2. 传感器数据处理函数

```c
void SensorDataProcess(void)
{
  /* 获取JY901S传感器数据 */
  JY901S_GetData(&jy901s_handle, &sensor_data);
  
  /* 读取灰度传感器数据 */
  GrayScale_ReadData();
  
  /* 清屏并显示数据 */
  OLED_Clear();
  
  /* 显示系统状态 */
  DisplaySystemStatus();
  
  /* 显示JY901S数据 */
  DisplayJY901SData();
  
  /* 显示灰度传感器数据 */
  DisplayGrayscaleData();
}
```

### 3. JY901S数据获取和处理

```c
void DisplayJY901SData(void)
{
  // 第2行: Roll和Pitch角度 (-180° ~ +180°)
  OLED_ShowStr(0, 1, "R:", 8);
  OLED_ShowSignedAngle(16, 1, (int16_t)sensor_data.angle[0]);
  OLED_ShowStr(56, 1, "P:", 8);
  OLED_ShowSignedAngle(72, 1, (int16_t)sensor_data.angle[1]);
  
  // 第3行: Yaw角度
  OLED_ShowStr(0, 2, "Y:", 8);
  OLED_ShowSignedAngle(16, 2, (int16_t)sensor_data.angle[2]);
}

void OLED_ShowSignedAngle(uint8_t x, uint8_t y, int16_t angle)
{
  if (angle < 0) {
    OLED_ShowStr(x, y, "-", 8);
    OLED_ShowNum(x + 8, y, -angle, 3, 8);
  } else {
    OLED_ShowStr(x, y, " ", 8);  // 正数前面显示空格对齐
    OLED_ShowNum(x + 8, y, angle, 3, 8);
  }
}
```

### 4. 灰度传感器数据获取和处理

```c
void GrayScale_ReadData(void)
{
  if (gray_sensor_connected) {
    // 读取数字量数据 (8位二进制)
    gray_digital_data = IIC_Get_Digtal();
    
    // 读取模拟量数据 (8路0-255)
    IIC_Get_Anolog(gray_analog_data, 8);
  }
}

void DisplayGrayscaleData(void)
{
  if (gray_sensor_connected) {
    // 显示数字量数据
    OLED_ShowStr(0, 3, "G:", 8);
    
    // 显示前4位数字量
    for (int i = 0; i < 4; i++) {
      uint8_t bit = GET_NTH_BIT(gray_digital_data, i + 1);
      OLED_ShowNum(16 + i * 8, 3, bit, 1, 8);
    }
    
    // 显示后4位数字量
    for (int i = 0; i < 4; i++) {
      uint8_t bit = GET_NTH_BIT(gray_digital_data, i + 5);
      OLED_ShowNum(56 + i * 8, 3, bit, 1, 8);
    }
    
    // 显示模拟量平均值
    uint16_t avg = 0;
    for (int i = 0; i < 8; i++) {
      avg += gray_analog_data[i];
    }
    avg /= 8;
    OLED_ShowStr(96, 3, "A:", 8);
    OLED_ShowNum(112, 3, avg, 3, 8);
  } else {
    OLED_ShowStr(0, 3, "Gray: Disconnected", 8);
  }
}
```

### 5. 系统状态显示

```c
void DisplaySystemStatus(void)
{
  // 第1行: 系统标识和状态
  OLED_ShowStr(0, 0, "JY+Gray", 8);
  OLED_ShowStr(56, 0, "RX:", 8);
  OLED_ShowNum(80, 0, rx_count % 1000, 3, 8);  // UART接收计数
  
  // 传感器连接状态
  if (gray_sensor_connected) {
    OLED_ShowStr(104, 0, "OK", 8);
  } else {
    OLED_ShowStr(104, 0, "ER", 8);
  }
}
```

---

## 📊 数据格式说明

### JY901S传感器数据

#### 角度数据 (sensor_data.angle[])
```c
float roll  = sensor_data.angle[0];   // Roll角  (-180° ~ +180°)
float pitch = sensor_data.angle[1];   // Pitch角 (-180° ~ +180°)
float yaw   = sensor_data.angle[2];   // Yaw角   (-180° ~ +180°)
```

#### 加速度数据 (sensor_data.acc[])
```c
float acc_x = sensor_data.acc[0];     // X轴加速度 (g)
float acc_y = sensor_data.acc[1];     // Y轴加速度 (g)
float acc_z = sensor_data.acc[2];     // Z轴加速度 (g)
```

#### 角速度数据 (sensor_data.gyro[])
```c
float gyro_x = sensor_data.gyro[0];   // X轴角速度 (°/s)
float gyro_y = sensor_data.gyro[1];   // Y轴角速度 (°/s)
float gyro_z = sensor_data.gyro[2];   // Z轴角速度 (°/s)
```

### 灰度传感器数据

#### 数字量数据 (gray_digital_data)
```c
uint8_t digital = gray_digital_data;  // 8位二进制数据
// 每位代表一个探头: 0=白色/浅色, 1=黑色/深色
// 位0 = 探头1, 位1 = 探头2, ..., 位7 = 探头8

// 获取单个探头状态
uint8_t probe1 = GET_NTH_BIT(digital, 1);  // 探头1状态
uint8_t probe8 = GET_NTH_BIT(digital, 8);  // 探头8状态
```

#### 模拟量数据 (gray_analog_data[])
```c
uint8_t analog[8] = {0};              // 8路模拟量数据
IIC_Get_Anolog(analog, 8);            // 读取所有通道

// 每个值范围: 0-255
// 0 = 最白, 255 = 最黑
for (int i = 0; i < 8; i++) {
  printf("Channel %d: %d\n", i+1, analog[i]);
}
```

---

## 🎮 实际使用示例

### 示例1: 机器人姿态监控
```c
void RobotAttitudeMonitor(void)
{
  JY901S_GetData(&jy901s_handle, &sensor_data);
  
  // 检查机器人是否倾斜
  if (abs(sensor_data.angle[0]) > 30 || abs(sensor_data.angle[1]) > 30) {
    printf("Warning: Robot tilted! Roll: %.1f, Pitch: %.1f\n", 
           sensor_data.angle[0], sensor_data.angle[1]);
  }
  
  // 检查旋转状态
  if (abs(sensor_data.gyro[2]) > 100) {
    printf("Robot is turning fast: %.1f °/s\n", sensor_data.gyro[2]);
  }
}
```

### 示例2: 线路跟踪
```c
void LineFollowing(void)
{
  GrayScale_ReadData();
  
  if (gray_sensor_connected) {
    uint8_t line_position = 0;
    uint8_t line_detected = 0;
    
    // 分析线路位置
    for (int i = 0; i < 8; i++) {
      if (GET_NTH_BIT(gray_digital_data, i + 1)) {
        line_position += (i + 1);
        line_detected++;
      }
    }
    
    if (line_detected > 0) {
      line_position /= line_detected;  // 计算平均位置
      
      if (line_position < 4) {
        printf("Line on LEFT, turn left\n");
      } else if (line_position > 5) {
        printf("Line on RIGHT, turn right\n");
      } else {
        printf("Line in CENTER, go straight\n");
      }
    } else {
      printf("No line detected\n");
    }
  }
}
```

### 示例3: 综合导航系统
```c
void NavigationSystem(void)
{
  // 获取所有传感器数据
  JY901S_GetData(&jy901s_handle, &sensor_data);
  GrayScale_ReadData();
  
  // 姿态稳定性检查
  bool attitude_stable = (abs(sensor_data.angle[0]) < 10 && 
                         abs(sensor_data.angle[1]) < 10);
  
  // 线路检测
  bool line_detected = (gray_digital_data != 0);
  
  // 导航决策
  if (!attitude_stable) {
    printf("Stabilizing attitude...\n");
    // 姿态调整逻辑
  } else if (line_detected) {
    printf("Following line...\n");
    LineFollowing();
  } else {
    printf("Free navigation mode\n");
    // 自由导航逻辑
  }
}
```
