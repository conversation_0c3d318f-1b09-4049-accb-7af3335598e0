#ifndef __GRAYSCALE_SENSOR_H
#define __GRAYSCALE_SENSOR_H

#include "main.h"
#include <stdbool.h>
#include <stdint.h>

/**********************************************************
*** 灰度传感器测试库
*** 编写作者：米醋电子工作室技术团队
*** 版本：V1.0
*** 日期：2025-07-29
*** 功能：提供完整的灰度传感器测试和校准功能
**********************************************************/

/* 灰度传感器配置参数 */
#define GRAYSCALE_ADC_CHANNEL       ADC_CHANNEL_0    // ADC通道 (PA0)
#define GRAYSCALE_SAMPLE_TIME       ADC_SAMPLETIME_15CYCLES
#define GRAYSCALE_VREF              3.3f             // 参考电压
#define GRAYSCALE_ADC_RESOLUTION    4096             // 12位ADC分辨率

/* 测试配置参数 */
#define TEST_SAMPLE_INTERVAL        100              // 采样间隔 (ms)
#define TEST_DISPLAY_INTERVAL       500              // 显示间隔 (ms)
#define TEST_CALIBRATION_SAMPLES    100              // 校准采样次数

/* 灰度传感器数据结构 */
typedef struct {
    uint16_t raw_value;      // ADC原始值 (0-4095)
    float voltage;           // 电压值 (0-3.3V)
    uint8_t grayscale;       // 灰度值 (0-255)
    uint32_t timestamp;      // 时间戳
    bool valid;              // 数据有效标志
} GrayscaleSensor_t;

/* 校准数据结构 */
typedef struct {
    uint16_t min_value;      // 校准最小值
    uint16_t max_value;      // 校准最大值
    bool calibrated;         // 校准完成标志
    uint32_t sample_count;   // 校准样本数
} GrayscaleCalibration_t;

/* 统计数据结构 */
typedef struct {
    uint32_t total_samples;  // 总样本数
    uint16_t min_value;      // 最小值
    uint16_t max_value;      // 最大值
    float average_value;     // 平均值
    uint32_t start_time;     // 开始时间
} GrayscaleStats_t;

/* 函数声明 */

/* 初始化和配置 */
void GrayscaleSensor_Init(void);
void GrayscaleSensor_Reset(void);
void GrayscaleSensor_SetSampleInterval(uint32_t interval_ms);

/* 数据读取和处理 */
void GrayscaleSensor_ReadValue(void);
void GrayscaleSensor_ProcessData(void);
GrayscaleSensor_t* GrayscaleSensor_GetData(void);
bool GrayscaleSensor_IsDataReady(void);

/* 校准功能 */
void GrayscaleSensor_StartCalibration(void);
void GrayscaleSensor_StopCalibration(void);
bool GrayscaleSensor_IsCalibrating(void);
GrayscaleCalibration_t* GrayscaleSensor_GetCalibration(void);
void GrayscaleSensor_SaveCalibration(void);
void GrayscaleSensor_LoadCalibration(void);

/* 统计功能 */
GrayscaleStats_t* GrayscaleSensor_GetStats(void);
void GrayscaleSensor_ResetStats(void);
void GrayscaleSensor_PrintStats(void);

/* 显示和调试 */
void GrayscaleSensor_DisplayResults(void);
void GrayscaleSensor_PrintHeader(void);
void GrayscaleSensor_PrintCalibrationInfo(void);

/* 测试功能 */
void GrayscaleSensor_RunTest(uint32_t duration_ms);
void GrayscaleSensor_RunCalibrationTest(void);
bool GrayscaleSensor_SelfTest(void);

/* 工具函数 */
uint8_t GrayscaleSensor_RawToGrayscale(uint16_t raw_value);
float GrayscaleSensor_RawToVoltage(uint16_t raw_value);
uint16_t GrayscaleSensor_VoltageToRaw(float voltage);

#endif // __GRAYSCALE_SENSOR_H
