# STM32F407多传感器系统API调度参考

## 📋 概述

本文档详细说明如何在STM32F407项目中调度和使用JY901S九轴传感器、8路灰度传感器和电机驱动系统的API函数，为开发具体任务逻辑提供完整的函数调用参考。

---

## 🎯 JY901S九轴传感器调度

### 数据结构
```c
// 全局传感器数据结构
extern JY901S_Data sensor_data;  // 在main.c中定义

// 数据结构定义
typedef struct {
    // 角度数据 (已转换为-180°到+180°范围)
    float roll;     // 横滚角 (绕X轴旋转)
    float pitch;    // 俯仰角 (绕Y轴旋转) 
    float yaw;      // 偏航角 (绕Z轴旋转)
    
    // 加速度数据 (单位: g)
    float acc_x, acc_y, acc_z;
    
    // 角速度数据 (单位: °/s)
    float gyro_x, gyro_y, gyro_z;
    
    // 磁场数据 (单位: 高斯)
    float mag_x, mag_y, mag_z;
} JY901S_Data;
```

### 核心调度函数

#### 1. 获取传感器数据
```c
// 获取最新的传感器数据 (推荐在任务逻辑中使用)
void JY901S_GetData(JY901S_Handle *handle, JY901S_Data *data);

// 使用示例
JY901S_Data current_data;
JY901S_GetData(&jy901s_handle, &current_data);

// 访问三个角度
float roll_angle = current_data.roll;    // -180.0f 到 +180.0f
float pitch_angle = current_data.pitch;  // -180.0f 到 +180.0f  
float yaw_angle = current_data.yaw;      // -180.0f 到 +180.0f
```

#### 2. 直接访问全局数据
```c
// 直接访问全局传感器数据 (数据通过中断自动更新)
extern JY901S_Data sensor_data;

// 使用示例
float current_roll = sensor_data.roll;
float current_pitch = sensor_data.pitch;
float current_yaw = sensor_data.yaw;
```

### 任务逻辑集成示例
```c
void MyTaskLogic(void)
{
    // 方法1: 获取最新数据
    JY901S_Data current_sensor;
    JY901S_GetData(&jy901s_handle, &current_sensor);
    
    // 方法2: 直接使用全局数据
    float roll = sensor_data.roll;
    float pitch = sensor_data.pitch;
    float yaw = sensor_data.yaw;
    
    // 基于角度的控制逻辑
    if (roll > 10.0f) {
        // 向右倾斜，执行平衡动作
    } else if (roll < -10.0f) {
        // 向左倾斜，执行平衡动作
    }
}
```

---

## 🔍 8路灰度传感器调度

### 数据结构
```c
// 全局灰度传感器数据
extern uint8_t gray_digital_data;      // 数字量数据 (8位，每位代表一路)
extern uint16_t gray_analog_data[8];   // 模拟量数据 (8路，0-4095)
extern uint8_t gray_sensor_connected;  // 连接状态 (1=已连接, 0=未连接)
```

### 核心调度函数

#### 1. 读取传感器数据
```c
// 读取灰度传感器数据 (更新全局变量)
void GrayScale_ReadData(void);

// 使用示例
GrayScale_ReadData();  // 更新全局数据

// 访问数字量数据 (每位代表一路传感器)
uint8_t digital_data = gray_digital_data;
uint8_t sensor0_digital = (digital_data >> 0) & 0x01;  // 第0路
uint8_t sensor1_digital = (digital_data >> 1) & 0x01;  // 第1路
// ... 依此类推到第7路

// 访问模拟量数据 (0-4095范围)
uint16_t sensor0_analog = gray_analog_data[0];  // 第0路模拟值
uint16_t sensor1_analog = gray_analog_data[1];  // 第1路模拟值
// ... 依此类推到gray_analog_data[7]
```

#### 2. 连接状态检查
```c
// 检查传感器连接状态
if (gray_sensor_connected) {
    // 传感器已连接，可以使用数据
    GrayScale_ReadData();
    // 处理数据...
} else {
    // 传感器未连接，执行错误处理
}
```

### 任务逻辑集成示例
```c
void LineFollowingTask(void)
{
    // 检查传感器连接状态
    if (!gray_sensor_connected) {
        // 传感器未连接，停止电机
        Motor_SetSpeed(&motor_left, 0.0f);
        Motor_SetSpeed(&motor_right, 0.0f);
        return;
    }
    
    // 读取最新数据
    GrayScale_ReadData();
    
    // 解析数字量数据 (黑线检测)
    uint8_t line_sensors = gray_digital_data;
    
    // 循迹逻辑示例
    if (line_sensors == 0b00011000) {
        // 中间两个传感器检测到线，直行
        Motor_SetSpeed(&motor_left, 50.0f);
        Motor_SetSpeed(&motor_right, 50.0f);
    } else if (line_sensors & 0b11110000) {
        // 线在右侧，左转
        Motor_SetSpeed(&motor_left, 20.0f);
        Motor_SetSpeed(&motor_right, 60.0f);
    } else if (line_sensors & 0b00001111) {
        // 线在左侧，右转
        Motor_SetSpeed(&motor_left, 60.0f);
        Motor_SetSpeed(&motor_right, 20.0f);
    }
    
    // 使用模拟量数据进行精确控制
    uint16_t left_sum = gray_analog_data[0] + gray_analog_data[1] + gray_analog_data[2];
    uint16_t right_sum = gray_analog_data[5] + gray_analog_data[6] + gray_analog_data[7];
    
    // 基于模拟量的PID控制
    float error = (float)(right_sum - left_sum) / 1000.0f;
    // PID计算...
}
```

---

## ⚙️ 电机驱动调度

### 数据结构
```c
// 全局电机实例
extern Motor_t motor_left;   // 左电机
extern Motor_t motor_right;  // 右电机

// 电机状态结构 (只读，用于状态查询)
typedef struct {
    float speed;                    // 当前速度 (-100.0f 到 +100.0f)
    MotorState_t state;            // 电机状态
    uint8_t enable;                // 使能状态
    MotorDecayMode_t decay_mode;   // 衰减模式
} Motor_t;
```

### 核心调度函数

#### 1. 速度控制
```c
// 设置电机速度 (-100.0f 到 +100.0f)
void Motor_SetSpeed(Motor_t* motor, float speed);

// 使用示例
Motor_SetSpeed(&motor_left, 50.0f);   // 左电机正转50%速度
Motor_SetSpeed(&motor_right, -30.0f); // 右电机反转30%速度
Motor_SetSpeed(&motor_left, 0.0f);    // 左电机停止
```

#### 2. 电机使能控制
```c
// 启用/禁用电机
void Motor_Enable(Motor_t* motor, uint8_t enable);

// 使用示例
Motor_Enable(&motor_left, 1);   // 启用左电机
Motor_Enable(&motor_right, 0);  // 禁用右电机
```

#### 3. 衰减模式设置
```c
// 设置衰减模式
void Motor_SetDecayMode(Motor_t* motor, MotorDecayMode_t mode);

// 使用示例
Motor_SetDecayMode(&motor_left, MOTOR_DECAY_FAST);  // 快衰减(更好制动)
Motor_SetDecayMode(&motor_right, MOTOR_DECAY_SLOW); // 慢衰减(更平滑)
```

#### 4. 状态查询
```c
// 查询电机状态
float current_speed = motor_left.speed;        // 获取当前速度
uint8_t is_enabled = motor_left.enable;        // 获取使能状态
MotorState_t state = motor_left.state;         // 获取运行状态
```

### 任务逻辑集成示例
```c
void MotorControlTask(void)
{
    // 基于传感器数据的电机控制
    JY901S_GetData(&jy901s_handle, &sensor_data);
    
    // 平衡控制示例
    float roll_error = sensor_data.roll;  // 横滚角误差
    
    if (roll_error > 5.0f) {
        // 向右倾斜，左电机加速，右电机减速
        Motor_SetSpeed(&motor_left, 60.0f);
        Motor_SetSpeed(&motor_right, 40.0f);
    } else if (roll_error < -5.0f) {
        // 向左倾斜，右电机加速，左电机减速
        Motor_SetSpeed(&motor_left, 40.0f);
        Motor_SetSpeed(&motor_right, 60.0f);
    } else {
        // 平衡状态，同速前进
        Motor_SetSpeed(&motor_left, 50.0f);
        Motor_SetSpeed(&motor_right, 50.0f);
    }
    
    // 紧急停止逻辑
    if (sensor_data.pitch > 45.0f || sensor_data.pitch < -45.0f) {
        // 倾斜角度过大，紧急停止
        Motor_SetSpeed(&motor_left, 0.0f);
        Motor_SetSpeed(&motor_right, 0.0f);
    }
}
```

---

## 🔄 系统集成调度模式

### 主循环集成
```c
void MainTaskLoop(void)
{
    while (1) {
        // 1. 获取传感器数据 (数据通过中断自动更新)
        JY901S_GetData(&jy901s_handle, &sensor_data);
        
        // 2. 读取灰度传感器
        if (gray_sensor_connected) {
            GrayScale_ReadData();
        }
        
        // 3. 执行任务逻辑
        MyCustomTask();
        
        // 4. 更新显示 (可选)
        UpdateDisplay();
        
        // 5. 延时控制循环频率
        HAL_Delay(100);  // 10Hz更新频率
    }
}
```

### 自定义任务模板
```c
void MyCustomTask(void)
{
    // 步骤1: 获取传感器数据
    float roll = sensor_data.roll;
    float pitch = sensor_data.pitch;
    float yaw = sensor_data.yaw;
    
    // 步骤2: 获取灰度数据
    uint8_t line_data = gray_digital_data;
    uint16_t* analog_data = gray_analog_data;
    
    // 步骤3: 任务逻辑处理
    // ... 您的控制算法 ...
    
    // 步骤4: 电机控制输出
    Motor_SetSpeed(&motor_left, left_speed);
    Motor_SetSpeed(&motor_right, right_speed);
}
```

---

## 📊 数据访问优先级建议

### 高频访问 (主循环中)
- `sensor_data.roll/pitch/yaw` - 姿态角度
- `gray_digital_data` - 灰度数字量
- `Motor_SetSpeed()` - 电机速度控制

### 中频访问 (根据需要)
- `gray_analog_data[]` - 灰度模拟量
- `Motor_SetDecayMode()` - 衰减模式调整
- `gray_sensor_connected` - 连接状态检查

### 低频访问 (初始化或异常处理)
- `Motor_Enable()` - 电机使能控制
- `JY901S_ResetParser()` - 传感器复位

---

## ⚠️ 重要注意事项

1. **数据同步**: JY901S数据通过中断更新，灰度数据需主动调用`GrayScale_ReadData()`
2. **错误处理**: 始终检查`gray_sensor_connected`状态
3. **速度限制**: 电机速度范围为-100.0f到+100.0f
4. **循环频率**: 建议主循环频率为10Hz (100ms延时)
5. **角度范围**: JY901S角度已转换为-180°到+180°范围

---

*文档版本: v1.0*  
*最后更新: 2025-07-28*
