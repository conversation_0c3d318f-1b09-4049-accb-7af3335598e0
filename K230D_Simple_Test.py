#####################################################################################################
# @file         K230D_Simple_Test.py
# <AUTHOR>
# @version      V1.0
# @date         2025-07-29
# @brief        K230D简化测试程序 - 发送固定坐标数据用于STM32调试
# @license      Copyright (c) 2025, 米醋电子工作室
#####################################################################################################
# @attention
#
# 实验平台: 正点原子 K230D BOX开发板
# 功能描述: 发送固定的坐标数据，用于调试STM32接收功能
# 硬件连接: UART1_TX(Pin40), UART1_RX(Pin41) → STM32 USART6
# 数据格式: "RED_CENTER:X=320,Y=240\n"
#
#####################################################################################################

import time
from machine import Pin, UART, FPIOA

# 实例化FPIOA
fpioa = FPIOA()

# 配置UART1引脚功能
fpioa.set_function(40, FPIOA.UART1_TXD)  # Pin40配置为UART1发送
fpioa.set_function(41, FPIOA.UART1_RXD)  # Pin41配置为UART1接收

# 配置状态指示LED
fpioa.set_function(59, FPIOA.GPIO59)     # 蓝色LED
fpioa.set_function(61, FPIOA.GPIO61)     # 红色LED

# 初始化GPIO对象
led_blue = Pin(59, Pin.OUT, pull=Pin.PULL_NONE, drive=7)
led_red = Pin(61, Pin.OUT, pull=Pin.PULL_NONE, drive=7)

# 初始化UART1对象
uart1 = UART(UART.UART1, baudrate=115200, bits=UART.EIGHTBITS, 
             parity=UART.PARITY_NONE, stop=UART.STOPBITS_ONE)

print("K230D Simple Test Program")
print("UART1 Config: 115200-8N1 → STM32 USART6")
print("Sending test data every 1 second...")

# 初始化LED指示
led_blue.value(1)
led_red.value(1)
time.sleep_ms(500)
led_blue.value(0)
led_red.value(0)

# 测试数据
test_coordinates = [
    (320, 240),  # 屏幕中心
    (100, 100),  # 左上角
    (540, 380),  # 右下角
    (320, 100),  # 上中
    (320, 380),  # 下中
    (100, 240),  # 左中
    (540, 240),  # 右中
]

coord_index = 0
packet_count = 0

try:
    while True:
        # 获取当前测试坐标
        x, y = test_coordinates[coord_index]
        
        # 构造数据包
        data_packet = f"RED_CENTER:X={x},Y={y}\n"
        
        # 发送数据
        uart1.write(data_packet)
        packet_count += 1
        
        # LED指示
        led_blue.value(1)
        time.sleep_ms(50)
        led_blue.value(0)
        
        # 打印发送信息
        print(f"[{packet_count:04d}] Sent: {data_packet.strip()}")
        
        # 切换到下一个坐标
        coord_index = (coord_index + 1) % len(test_coordinates)
        
        # 每10个包闪烁红色LED
        if packet_count % 10 == 0:
            led_red.value(1)
            time.sleep_ms(100)
            led_red.value(0)
        
        # 等待1秒
        time.sleep(1)
        
except KeyboardInterrupt:
    print("\nTest stopped by user")
except Exception as e:
    print(f"Error: {e}")
finally:
    # 清理资源
    led_blue.value(0)
    led_red.value(0)
    print("K230D Simple Test Stopped")
