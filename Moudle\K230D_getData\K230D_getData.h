#ifndef K230D_GETDATA_H
#define K230D_GETDATA_H

#ifdef __cplusplus
extern "C" {
#endif

#include "main.h"
#include <string.h>
#include <stdio.h>

/**
 * @file    K230D_getData.h
 * <AUTHOR>
 * @version V1.0
 * @date    2025-07-29
 * @brief   K230D数据接收模块 - DMA环形缓冲区实现
 * @license Copyright (c) 2025, 米醋电子工作室
 */

/* 配置参数 */
#define K230D_DMA_BUFFER_SIZE   256     // DMA环形缓冲区大小
#define K230D_PACKET_BUFFER_SIZE 64     // 数据包处理缓冲区大小

/* 数据结构定义 */
typedef struct {
    // 坐标数据
    int16_t x;                          // X坐标
    int16_t y;                          // Y坐标

    // 接收统计
    uint32_t total_bytes;               // 总接收字节数
    uint32_t processed_packets;         // 已处理数据包数
    uint32_t valid_packets;             // 有效数据包数

    // 状态信息
    uint8_t last_byte;                  // 最后接收的字节
    uint16_t available_data;            // 缓冲区可用数据量
    char last_packet[K230D_PACKET_BUFFER_SIZE]; // 最后接收的完整数据包

    // 数据更新检测
    uint32_t last_update_tick;          // 最后更新时间戳
    uint8_t data_updated;               // 数据更新标志

    // 内部状态
    uint8_t initialized;                // 初始化标志
} K230D_Data_t;

/* 全局数据结构 */
extern K230D_Data_t g_k230d_data;

/* DMA调试变量 */
extern volatile uint16_t dma_rx_tail;
extern uint8_t dma_rx_buffer[K230D_DMA_BUFFER_SIZE];

/* 函数声明 */

/**
 * @brief 初始化K230D数据接收模块
 * @param uart_handle UART句柄指针
 * @retval HAL_StatusTypeDef 初始化状态
 */
HAL_StatusTypeDef K230D_Init(UART_HandleTypeDef *uart_handle);

/**
 * @brief 处理K230D接收数据
 * @note 应在主循环中调用此函数
 */
void K230D_ProcessData(void);

/**
 * @brief 获取K230D坐标数据
 * @param x 输出X坐标指针
 * @param y 输出Y坐标指针
 * @retval uint8_t 1-有效数据, 0-无效数据
 */
uint8_t K230D_GetCoordinates(int16_t *x, int16_t *y);

/**
 * @brief 获取接收统计信息
 * @retval K230D_Data_t* 数据结构指针
 */
K230D_Data_t* K230D_GetStats(void);

/**
 * @brief 获取DMA环形缓冲区可用数据量
 * @retval uint16_t 可用数据字节数
 */
uint16_t K230D_GetAvailableData(void);

/**
 * @brief 重置K230D接收统计
 */
void K230D_ResetStats(void);

/**
 * @brief 检查数据是否有更新
 * @retval uint8_t 1-有新数据, 0-无新数据
 */
uint8_t K230D_IsDataUpdated(void);

/**
 * @brief 清除数据更新标志
 */
void K230D_ClearUpdateFlag(void);

/**
 * @brief 获取数据年龄(毫秒)
 * @retval uint32_t 距离最后更新的毫秒数
 */
uint32_t K230D_GetDataAge(void);

/**
 * @brief 清理过期数据和缓冲区
 * @note 建议定期调用以清理积压数据
 */
void K230D_CleanupBuffers(void);

/**
 * @brief 反初始化K230D数据接收模块
 */
void K230D_DeInit(void);

#ifdef __cplusplus
}
#endif

#endif /* K230D_GETDATA_H */
